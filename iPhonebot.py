from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import <PERSON><PERSON>hains
from selenium.webdriver.common.keys import Keys
import time
import random
import os
import logging
from datetime import datetime, timedelta
import threading
from queue import Queue
import concurrent.futures
import tkinter as tk
from tkinter import ttk, messagebox
import queue
import requests
from requests.auth import HTTPProxyAuth
import json
import schedule
import sys
from selenium.common.exceptions import TimeoutException
import tempfile
import zipfile
import string
import re
import subprocess
import webbrowser
import sqlite3
from typing import List, Dict, Optional, Tuple
import collections
import platform

# 性能统计和监控系统
class PerformanceMonitor:
    def __init__(self):
        self.stats = {
            'total_attempts': 0,
            'successful_attempts': 0,
            'failed_attempts': 0,
            'step_times': collections.defaultdict(list),
            'error_counts': collections.defaultdict(int),
            'account_performance': collections.defaultdict(dict),
            'start_time': None,
            'end_time': None
        }
        self.lock = threading.Lock()

    def start_session(self):
        """开始新的监控会话"""
        with self.lock:
            self.stats['start_time'] = time.time()

    def end_session(self):
        """结束监控会话"""
        with self.lock:
            self.stats['end_time'] = time.time()

    def record_attempt(self, account_email, success=True):
        """记录尝试结果"""
        with self.lock:
            self.stats['total_attempts'] += 1
            if success:
                self.stats['successful_attempts'] += 1
            else:
                self.stats['failed_attempts'] += 1

            if account_email not in self.stats['account_performance']:
                self.stats['account_performance'][account_email] = {
                    'attempts': 0, 'successes': 0, 'failures': 0, 'step_times': {}
                }

            self.stats['account_performance'][account_email]['attempts'] += 1
            if success:
                self.stats['account_performance'][account_email]['successes'] += 1
            else:
                self.stats['account_performance'][account_email]['failures'] += 1

    def record_step_time(self, step_name, duration, account_email=None):
        """记录步骤耗时"""
        with self.lock:
            self.stats['step_times'][step_name].append(duration)
            if account_email and account_email in self.stats['account_performance']:
                if 'step_times' not in self.stats['account_performance'][account_email]:
                    self.stats['account_performance'][account_email]['step_times'] = {}
                if step_name not in self.stats['account_performance'][account_email]['step_times']:
                    self.stats['account_performance'][account_email]['step_times'][step_name] = []
                self.stats['account_performance'][account_email]['step_times'][step_name].append(duration)

    def record_error(self, error_type, account_email=None):
        """记录错误"""
        with self.lock:
            self.stats['error_counts'][error_type] += 1
            if account_email and account_email in self.stats['account_performance']:
                if 'errors' not in self.stats['account_performance'][account_email]:
                    self.stats['account_performance'][account_email]['errors'] = collections.defaultdict(int)
                self.stats['account_performance'][account_email]['errors'][error_type] += 1

    def get_summary(self):
        """获取统计摘要"""
        with self.lock:
            total_time = 0
            if self.stats['start_time'] and self.stats['end_time']:
                total_time = self.stats['end_time'] - self.stats['start_time']

            success_rate = 0
            if self.stats['total_attempts'] > 0:
                success_rate = (self.stats['successful_attempts'] / self.stats['total_attempts']) * 100

            avg_step_times = {}
            for step, times in self.stats['step_times'].items():
                if times:
                    avg_step_times[step] = sum(times) / len(times)

            return {
                'total_time': total_time,
                'success_rate': success_rate,
                'total_attempts': self.stats['total_attempts'],
                'successful_attempts': self.stats['successful_attempts'],
                'failed_attempts': self.stats['failed_attempts'],
                'avg_step_times': avg_step_times,
                'top_errors': dict(sorted(self.stats['error_counts'].items(), key=lambda x: x[1], reverse=True)[:5]),
                'account_count': len(self.stats['account_performance'])
            }

    def get_detailed_report(self):
        """获取详细报告"""
        with self.lock:
            return {
                'summary': self.get_summary(),
                'account_performance': dict(self.stats['account_performance']),
                'step_times': dict(self.stats['step_times']),
                'error_counts': dict(self.stats['error_counts'])
            }

# 创建全局性能监控实例
performance_monitor = PerformanceMonitor()

# 智能重试装饰器
def smart_retry(max_attempts=3, delay_base=1, backoff_factor=2, exceptions=(Exception,)):
    """智能重试装饰器，支持指数退避"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt < max_attempts - 1:
                        delay = delay_base * (backoff_factor ** attempt)
                        print(f"⚠️ 第{attempt + 1}次尝试失败: {str(e)}, {delay}秒后重试...")
                        time.sleep(delay)
                    else:
                        print(f"❌ 所有重试尝试失败: {str(e)}")
            raise last_exception
        return wrapper
    return decorator

# 网络状态检测
def check_network_connectivity(url="https://www.apple.com.cn", timeout=10):
    """检测网络连接状态"""
    try:
        response = requests.get(url, timeout=timeout)
        return response.status_code == 200
    except:
        return False

# 浏览器健康检查
def check_driver_health(driver):
    """检查浏览器驱动是否健康"""
    try:
        driver.current_url
        driver.title
        return True
    except:
        return False

# 配置管理系统
class ConfigManager:
    def __init__(self, config_file="config.json"):
        self.config_file = config_file
        self.default_config = {
            "performance": {
                "max_concurrent_accounts": 6,
                "page_load_timeout": 30,
                "element_wait_timeout": 10,
                "retry_attempts": 3,
                "retry_delay": 2
            },
            "browser": {
                "headless": False,
                "window_size": "1024,768",
                "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
                "disable_images": True,
                "disable_css": False
            },
            "automation": {
                "random_delay_min": 1,
                "random_delay_max": 3,
                "typing_delay": 0.1,
                "click_delay": 0.5
            },
            "monitoring": {
                "enable_performance_tracking": True,
                "save_screenshots_on_error": True,
                "save_html_on_error": True,
                "log_level": "INFO"
            }
        }
        self.config = self.load_config()

    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置和用户配置
                return self.merge_config(self.default_config, config)
            else:
                self.save_config(self.default_config)
                return self.default_config.copy()
        except Exception as e:
            print(f"⚠️ 配置文件加载失败，使用默认配置: {e}")
            return self.default_config.copy()

    def save_config(self, config=None):
        """保存配置文件"""
        try:
            config_to_save = config or self.config
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_to_save, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"❌ 配置文件保存失败: {e}")
            return False

    def merge_config(self, default, user):
        """递归合并配置"""
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self.merge_config(result[key], value)
            else:
                result[key] = value
        return result

    def get(self, key_path, default=None):
        """获取配置值，支持点号分隔的路径"""
        keys = key_path.split('.')
        value = self.config
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        return value

    def set(self, key_path, value):
        """设置配置值"""
        keys = key_path.split('.')
        config = self.config
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        config[keys[-1]] = value
        self.save_config()

# 创建全局配置管理实例
config_manager = ConfigManager()

# 在文件开头合适位置添加异步关闭函数
def async_quit_driver(driver):
    import threading
    threading.Thread(target=lambda d=driver: d.quit(), daemon=True).start()

# # # # # # # # # # # # # # # # # # Apple ID账号管理类 (数据库底层操作代码) # # # # # # # # # # # # # # # # # # 
class AccountDatabase:
    def __init__(self, db_path="accounts.db"):
        """初始化数据库连接"""
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库表结构"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建账号表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS accounts (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        email TEXT UNIQUE NOT NULL,
                        password TEXT NOT NULL,
                        status TEXT DEFAULT 'ready',
                        url TEXT,
                        installment TEXT DEFAULT 'cmb',
                        remark TEXT,
                        last_used TIMESTAMP,
                        cooldown_minutes INTEGER DEFAULT 30,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        shipping_info TEXT
                    )
                ''')
                
                # 创建操作日志表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS operation_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        account_id INTEGER,
                        operation_type TEXT NOT NULL,
                        operation_details TEXT,
                        success BOOLEAN,
                        error_message TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (account_id) REFERENCES accounts (id)
                    )
                ''')
                
                # 创建触发器，自动更新updated_at字段
                cursor.execute('''
                    CREATE TRIGGER IF NOT EXISTS update_accounts_timestamp 
                    AFTER UPDATE ON accounts
                    BEGIN
                        UPDATE accounts SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
                    END
                ''')
                
                conn.commit()
                print(f"✅ 数据库初始化成功: {self.db_path}")
                
        except Exception as e:
            print(f"❌ 数据库初始化失败: {str(e)}")
            raise
    
    def add_account(self, account_data: Dict) -> bool:
        """添加新账号"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 检查邮箱是否已存在
                cursor.execute("SELECT id FROM accounts WHERE email = ?", (account_data['email'],))
                if cursor.fetchone():
                    print(f"⚠️ 邮箱 {account_data['email']} 已存在")
                    return False
                
                # 插入新账号
                cursor.execute('''
                    INSERT INTO accounts (email, password, status, url, installment, remark, cooldown_minutes, shipping_info)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    account_data['email'],
                    account_data['password'],
                    account_data.get('status', 'ready'),
                    account_data.get('url', ''),
                    account_data.get('installment', 'cmb'),
                    account_data.get('remark', ''),
                    account_data.get('cooldown_minutes', 30),
                    json.dumps(account_data.get('shipping_info', {}), ensure_ascii=False)
                ))
                
                account_id = cursor.lastrowid
                
                # 记录操作日志
                cursor.execute('''
                    INSERT INTO operation_logs (account_id, operation_type, operation_details, success)
                    VALUES (?, ?, ?, ?)
                ''', (account_id, 'ADD', f"添加账号: {account_data['email']}", True))
                
                conn.commit()
                print(f"✅ 成功添加账号: {account_data['email']}")
                return True
                
        except Exception as e:
            print(f"❌ 添加账号失败: {str(e)}")
            return False
    
    def update_account(self, email: str, update_data: Dict) -> bool:
        """更新账号信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 检查账号是否存在
                cursor.execute("SELECT id FROM accounts WHERE email = ?", (email,))
                result = cursor.fetchone()
                if not result:
                    print(f"⚠️ 账号 {email} 不存在")
                    return False
                
                account_id = result[0]
                
                # 构建更新语句
                update_fields = []
                update_values = []
                
                for field, value in update_data.items():
                    if field in ['email', 'password', 'status', 'url', 'installment', 'remark', 'cooldown_minutes']:
                        update_fields.append(f"{field} = ?")
                        update_values.append(value)
                    elif field == 'shipping_info':
                        update_fields.append("shipping_info = ?")
                        update_values.append(json.dumps(value, ensure_ascii=False))
                
                if not update_fields:
                    print("⚠️ 没有有效的更新字段")
                    return False
                
                update_values.append(email)
                
                # 执行更新
                cursor.execute(f'''
                    UPDATE accounts 
                    SET {', '.join(update_fields)}
                    WHERE email = ?
                ''', update_values)
                
                # 记录操作日志
                cursor.execute('''
                    INSERT INTO operation_logs (account_id, operation_type, operation_details, success)
                    VALUES (?, ?, ?, ?)
                ''', (account_id, 'UPDATE', f"更新账号: {email}", True))
                
                conn.commit()
                print(f"✅ 成功更新账号: {email}")
                return True
                
        except Exception as e:
            print(f"❌ 更新账号失败: {str(e)}")
            return False
    
    def delete_account(self, email: str) -> bool:
        """删除账号"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 检查账号是否存在
                cursor.execute("SELECT id FROM accounts WHERE email = ?", (email,))
                result = cursor.fetchone()
                if not result:
                    print(f"⚠️ 账号 {email} 不存在")
                    return False
                
                account_id = result[0]
                
                # 删除账号
                cursor.execute("DELETE FROM accounts WHERE email = ?", (email,))
                
                # 记录操作日志
                cursor.execute('''
                    INSERT INTO operation_logs (account_id, operation_type, operation_details, success)
                    VALUES (?, ?, ?, ?)
                ''', (account_id, 'DELETE', f"删除账号: {email}", True))
                
                conn.commit()
                print(f"✅ 成功删除账号: {email}")
                return True
                
        except Exception as e:
            print(f"❌ 删除账号失败: {str(e)}")
            return False
    
    def get_account(self, email: str) -> Optional[Dict]:
        """根据邮箱获取账号信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT id, email, password, status, url, installment, remark, 
                           last_used, cooldown_minutes, created_at, updated_at, shipping_info
                    FROM accounts 
                    WHERE email = ?
                ''', (email,))
                
                result = cursor.fetchone()
                if result:
                    shipping_info = result[11]
                    try:
                        shipping_info = json.loads(shipping_info) if shipping_info else {}
                    except:
                        shipping_info = {}
                    return {
                        'id': result[0],
                        'email': result[1],
                        'password': result[2],
                        'status': result[3],
                        'url': result[4],
                        'installment': result[5],
                        'remark': result[6],
                        'last_used': result[7],
                        'cooldown_minutes': result[8],
                        'created_at': result[9],
                        'updated_at': result[10],
                        'shipping_info': shipping_info
                    }
                return None
                
        except Exception as e:
            print(f"❌ 获取账号失败: {str(e)}")
            return None
    
    def get_all_accounts(self) -> List[Dict]:
        """获取所有账号"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT id, email, password, status, url, installment, remark, 
                           last_used, cooldown_minutes, created_at, updated_at, shipping_info
                    FROM accounts 
                    ORDER BY created_at DESC
                ''')
                
                results = cursor.fetchall()
                accounts = []
                
                for result in results:
                    shipping_info = result[11]
                    try:
                        shipping_info = json.loads(shipping_info) if shipping_info else {}
                    except:
                        shipping_info = {}
                    accounts.append({
                        'id': result[0],
                        'email': result[1],
                        'password': result[2],
                        'status': result[3],
                        'url': result[4],
                        'installment': result[5],
                        'remark': result[6],
                        'last_used': result[7],
                        'cooldown_minutes': result[8],
                        'created_at': result[9],
                        'updated_at': result[10],
                        'shipping_info': shipping_info
                    })
                
                return accounts
                
        except Exception as e:
            print(f"❌ 获取所有账号失败: {str(e)}")
            return []
    
    def get_accounts_by_status(self, status: str) -> List[Dict]:
        """根据状态获取账号"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT id, email, password, status, url, installment, remark, 
                           last_used, cooldown_minutes, created_at, updated_at, shipping_info
                    FROM accounts 
                    WHERE status = ?
                    ORDER BY created_at DESC
                ''', (status,))
                
                results = cursor.fetchall()
                accounts = []
                
                for result in results:
                    shipping_info = result[11]
                    try:
                        shipping_info = json.loads(shipping_info) if shipping_info else {}
                    except:
                        shipping_info = {}
                    accounts.append({
                        'id': result[0],
                        'email': result[1],
                        'password': result[2],
                        'status': result[3],
                        'url': result[4],
                        'installment': result[5],
                        'remark': result[6],
                        'last_used': result[7],
                        'cooldown_minutes': result[8],
                        'created_at': result[9],
                        'updated_at': result[10],
                        'shipping_info': shipping_info
                    })
                
                return accounts
                
        except Exception as e:
            print(f"❌ 根据状态获取账号失败: {str(e)}")
            return []
    
    def update_account_status(self, email: str, status: str, success: bool = True) -> bool:
        """更新账号状态"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 更新状态和最后使用时间
                cursor.execute('''
                    UPDATE accounts 
                    SET status = ?, last_used = CURRENT_TIMESTAMP
                    WHERE email = ?
                ''', (status, email))
                
                # 获取账号ID用于日志记录
                cursor.execute("SELECT id FROM accounts WHERE email = ?", (email,))
                result = cursor.fetchone()
                if result:
                    account_id = result[0]
                    
                    # 记录操作日志
                    operation_details = f"状态更新: {status}, 成功: {success}"
                    cursor.execute('''
                        INSERT INTO operation_logs (account_id, operation_type, operation_details, success)
                        VALUES (?, ?, ?, ?)
                    ''', (account_id, 'STATUS_UPDATE', operation_details, success))
                
                conn.commit()
                return True
                
        except Exception as e:
            print(f"❌ 更新账号状态失败: {str(e)}")
            return False
    
    def get_operation_logs(self, account_email: str = None, limit: int = 100) -> List[Dict]:
        """获取操作日志"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                if account_email:
                    cursor.execute('''
                        SELECT ol.id, ol.operation_type, ol.operation_details, 
                               ol.success, ol.error_message, ol.created_at,
                               a.email
                        FROM operation_logs ol
                        JOIN accounts a ON ol.account_id = a.id
                        WHERE a.email = ?
                        ORDER BY ol.created_at DESC
                        LIMIT ?
                    ''', (account_email, limit))
                else:
                    cursor.execute('''
                        SELECT ol.id, ol.operation_type, ol.operation_details, 
                               ol.success, ol.error_message, ol.created_at,
                               a.email
                        FROM operation_logs ol
                        JOIN accounts a ON ol.account_id = a.id
                        ORDER BY ol.created_at DESC
                        LIMIT ?
                    ''', (limit,))
                
                results = cursor.fetchall()
                logs = []
                
                for result in results:
                    logs.append({
                        'id': result[0],
                        'operation_type': result[1],
                        'operation_details': result[2],
                        'success': result[3],
                        'error_message': result[4],
                        'created_at': result[5],
                        'email': result[6]
                    })
                
                return logs
                
        except Exception as e:
            print(f"❌ 获取操作日志失败: {str(e)}")
            return []
    
    def backup_database(self, backup_path: str = None) -> bool:
        """备份数据库"""
        try:
            if not backup_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = f"backup_accounts_{timestamp}.db"
            
            with sqlite3.connect(self.db_path) as source_conn:
                with sqlite3.connect(backup_path) as backup_conn:
                    source_conn.backup(backup_conn)
            
            print(f"✅ 数据库备份成功: {backup_path}")
            return True
            
        except Exception as e:
            print(f"❌ 数据库备份失败: {str(e)}")
            return False
    
    def restore_database(self, backup_path: str) -> bool:
        """恢复数据库"""
        try:
            if not os.path.exists(backup_path):
                print(f"❌ 备份文件不存在: {backup_path}")
                return False
            
            # 先备份当前数据库
            current_backup = f"backup_before_restore_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
            self.backup_database(current_backup)
            
            # 恢复数据库
            with sqlite3.connect(backup_path) as source_conn:
                with sqlite3.connect(self.db_path) as target_conn:
                    source_conn.backup(target_conn)
            
            print(f"✅ 数据库恢复成功: {backup_path}")
            return True
            
        except Exception as e:
            print(f"❌ 数据库恢复失败: {str(e)}")
            return False
    
    def import_from_json(self, json_file_path: str) -> Tuple[int, int]:
        """从JSON文件导入账号"""
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                accounts_data = json.load(f)
            
            success_count = 0
            total_count = len(accounts_data)
            
            for account_data in accounts_data:
                # 兼容老数据
                if 'shipping_info' not in account_data:
                    account_data['shipping_info'] = {}
                if self.add_account(account_data):
                    success_count += 1
            
            print(f"✅ 导入完成: {success_count}/{total_count} 个账号成功导入")
            return success_count, total_count
            
        except Exception as e:
            print(f"❌ 导入账号失败: {str(e)}")
            return 0, 0
    
    def export_to_json(self, json_file_path: str) -> bool:
        """导出账号到JSON文件"""
        try:
            accounts = self.get_all_accounts()
            
            # 移除敏感信息
            export_data = []
            for account in accounts:
                export_account = account.copy()
                export_account['password'] = '***'  # 隐藏密码
                export_data.append(export_account)
            
            with open(json_file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False, default=str)
            
            print(f"✅ 导出完成: {len(export_data)} 个账号已导出到 {json_file_path}")
            return True
            
        except Exception as e:
            print(f"❌ 导出账号失败: {str(e)}")
            return False    
# # # # # # # # # # # # # # # # # # Apple ID账号数据库设置结束  # # # # # # # # # # # # # # # # # #

# 容量排序："1TB"、"512GB"、"256GB"、"128GB"
def capacity_sort_key(capacity):
    if 'TB' in capacity:
        return int(capacity.replace('TB', '')) * 1024
    elif 'GB' in capacity:
        return int(capacity.replace('GB', ''))
    else:
        return 0

# Pro Max优先
def pro_max_priority(model):
    return 0 if 'Pro Max' in model else 1

# 排序：Pro Max优先 > model > color > capacity（小到大）
with open('iphone_sku_links.json', 'r', encoding='utf-8') as f:
    sku_list = json.load(f)
sku_list_sorted = sorted(
    sku_list,
    key=lambda x: (
        pro_max_priority(x['model']),
        x['model'],
        x['color'],
        capacity_sort_key(x['capacity'])
    )
)
SKU_DISPLAY_LIST = [f"{item['model']} {item['color']} {item['capacity']}" for item in sku_list_sorted]
SKU_DISPLAY_MAP = {item['url']: f"{item['model']} {item['color']} {item['capacity']}" for item in sku_list_sorted}
SKU_REVERSE_MAP = {f"{item['model']} {item['color']} {item['capacity']}": item['url'] for item in sku_list_sorted}

# 批量修改机型对话框
class BatchModifyModelDialog:
    def __init__(self, parent, selected_emails):
        self.parent = parent
        self.selected_emails = selected_emails
        self.result = None
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("批量修改机型")
        self.dialog.geometry("500x430")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self._build_ui()
        self._center_dialog()
        
        # 等待对话框关闭
        self.dialog.wait_window()
    
    def _center_dialog(self):
        """居中显示对话框"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
    
    def _build_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ttk.Label(main_frame, text=f"批量修改机型 - 已选择 {len(self.selected_emails)} 个账号", font=("Arial", 12, "bold"))
        title_label.pack(pady=10)
        
        # 选中的账号列表
        list_frame = ttk.LabelFrame(main_frame, text="选中的账号")
        list_frame.pack(fill="x", pady=10)
        
        # 创建列表框显示选中的账号
        listbox = tk.Listbox(list_frame, height=6)
        listbox.pack(fill="x", padx=10, pady=10)
        
        for email in self.selected_emails:
            listbox.insert(tk.END, email)
        
        # 机型选择
        model_frame = ttk.LabelFrame(main_frame, text="选择新机型")
        model_frame.pack(fill="x", pady=10)
        
        ttk.Label(model_frame, text="机型:").pack(anchor="w", padx=10, pady=5)
        self.model_var = tk.StringVar()
        model_combo = ttk.Combobox(model_frame, textvariable=self.model_var, values=SKU_DISPLAY_LIST, state="readonly", width=50)
        model_combo.pack(fill="x", padx=10, pady=5)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=20)
        
        ttk.Button(button_frame, text="确定", command=self._confirm).pack(side="left", padx=10)
        ttk.Button(button_frame, text="取消", command=self._cancel).pack(side="left", padx=10)
    
    def _confirm(self):
        """确认修改"""
        selected_model = self.model_var.get()
        if not selected_model:
            messagebox.showwarning("警告", "请选择机型")
            return
        
        # 根据显示名称找到对应的URL
        new_url = None
        for url, display_name in SKU_DISPLAY_MAP.items():
            if display_name == selected_model:
                new_url = url
                break
        
        if not new_url:
            messagebox.showerror("错误", "无法找到对应的机型URL")
            return
        
        self.result = (selected_model, new_url)
        self.dialog.destroy()
    
    def _cancel(self):
        """取消"""
        self.dialog.destroy()

# 全局数据库实例
db = AccountDatabase()

# # # # # # # # # # # # # # # # # # 监控链接管理开始(UI界面)  # # # # # # # # # # # # # # # # # #
# 监控链接管理对话框(监控链接管理主界面)
class MonitorLinksDialog:
    def __init__(self, parent, db):
        self.parent = parent    
        self.db = db
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("监控链接管理")
        self.dialog.geometry("800x500")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self._build_ui()
        self._center_dialog()
        self._refresh_links()
    
    def _center_dialog(self):
        """居中显示对话框"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
    
    def _build_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 标题
        title = ttk.Label(main_frame, text="监控链接管理", font=("Arial", 16, "bold"))
        title.pack(pady=10)
        
        # 控制框架
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill="x", pady=5)
        
        ttk.Button(control_frame, text="添加监控链接", command=self._add_monitor_link).pack(side="left", padx=5)
        ttk.Button(control_frame, text="编辑监控链接", command=self._edit_monitor_link).pack(side="left", padx=5)
        ttk.Button(control_frame, text="删除监控链接", command=self._delete_monitor_link).pack(side="left", padx=5)
        ttk.Button(control_frame, text="刷新列表", command=self._refresh_links).pack(side="left", padx=5)
        ttk.Button(control_frame, text="测试链接", command=self._test_links).pack(side="left", padx=5)
        
        # 监控链接列表
        columns = ("名称", "URL", "检查间隔", "最大重试", "状态")
        self.tree = ttk.Treeview(main_frame, columns=columns, show="headings", height=15)
        
        column_widths = [150, 300, 100, 100, 100]
        for i, col in enumerate(columns):
            self.tree.heading(col, text=col)
            self.tree.column(col, width=column_widths[i], anchor="center")
        
        # 滚动条
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 绑定选择事件
        self.tree.bind("<<TreeviewSelect>>", self._on_select)
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief="sunken")
        status_bar.pack(fill="x", pady=5)
    
    def _refresh_links(self):
        """刷新监控链接列表"""
        # 清空现有项目
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 获取监控链接配置
        detector = SmartLinkDetector()
        links = detector.config.get('monitored_links', [])
        
        # 添加到树形视图
        for link in links:
            check_interval = link.get('check_interval', '随机5-8秒')
            if check_interval is None:
                check_interval = '随机5-8秒'
            elif isinstance(check_interval, (int, float)):
                check_interval = f"{check_interval}秒"
            
            self.tree.insert("", "end", values=(
                link.get('name', ''),
                link.get('url', ''),
                check_interval,
                link.get('max_retries', 10),
                '未测试'
            ), tags=(link.get('name', ''),))
        
        self.status_var.set(f"共 {len(links)} 个监控链接")
    
    def _on_select(self, event):
        """处理选择事件"""
        selection = self.tree.selection()
        if selection:
            item = selection[0]
            self.selected_link_name = self.tree.item(item, "tags")[0]
        else:
            self.selected_link_name = None
    
    def _add_monitor_link(self):
        """添加监控链接"""
        dialog = MonitorLinkDialog(self.dialog, "添加监控链接")
        if dialog.result:
            # 添加到配置文件
            detector = SmartLinkDetector()
            detector.add_link(
                dialog.result['url'],
                dialog.result['name'],
                dialog.result.get('check_interval'),
                dialog.result.get('max_retries')
            )
            self._refresh_links()
    
    def _edit_monitor_link(self):
        """编辑监控链接"""
        if not hasattr(self, 'selected_link_name') or not self.selected_link_name:
            messagebox.showwarning("警告", "请先选择一个监控链接")
            return
        
        # 获取选中的链接信息
        detector = SmartLinkDetector()
        links = detector.config.get('monitored_links', [])
        selected_link = None
        for link in links:
            if link.get('name') == self.selected_link_name:
                selected_link = link
                break
        
        if selected_link:
            dialog = MonitorLinkDialog(self.dialog, "编辑监控链接", selected_link)
            if dialog.result:
                # 更新配置文件
                detector = SmartLinkDetector()
                # 删除旧的链接
                detector.config['monitored_links'] = [
                    link for link in detector.config['monitored_links'] 
                    if link.get('name') != self.selected_link_name
                ]
                # 添加新的链接
                detector.add_link(
                    dialog.result['url'],
                    dialog.result['name'],
                    dialog.result.get('check_interval'),
                    dialog.result.get('max_retries')
                )
                self._refresh_links()
    
    def _delete_monitor_link(self):
        """删除监控链接"""
        if not hasattr(self, 'selected_link_name') or not self.selected_link_name:
            messagebox.showwarning("警告", "请先选择一个监控链接")
            return
        
        if messagebox.askyesno("确认删除", f"确定要删除监控链接 {self.selected_link_name} 吗？"):
            detector = SmartLinkDetector()
            detector.config['monitored_links'] = [
                link for link in detector.config['monitored_links'] 
                if link.get('name') != self.selected_link_name
            ]
            detector.save_config()
            self._refresh_links()
            messagebox.showinfo("成功", f"监控链接 {self.selected_link_name} 已删除")
    
    def _test_links(self):
        """测试监控链接"""
        detector = SmartLinkDetector()
        links = detector.config.get('monitored_links', [])
        
        if not links:
            messagebox.showinfo("提示", "没有配置监控链接")
            return
        
        # 创建测试窗口
        test_window = tk.Toplevel(self.dialog)
        test_window.title("测试监控链接")
        test_window.geometry("600x400")
        test_window.transient(self.dialog)
        
        # 测试结果显示
        text_widget = tk.Text(test_window, wrap="word")
        text_widget.pack(fill="both", expand=True, padx=10, pady=10)
        
        def run_tests():
            text_widget.delete(1.0, tk.END)
            text_widget.insert(tk.END, "开始测试监控链接...\n\n")
            
            for i, link in enumerate(links, 1):
                text_widget.insert(tk.END, f"测试 {i}/{len(links)}: {link.get('name')} - {link.get('url')}\n")
                text_widget.see(tk.END)
                test_window.update()
                
                try:
                    monitor = LinkMonitor(link.get('url'), link.get('check_interval'), link.get('max_retries', 10))
                    if monitor.check_link_availability():
                        text_widget.insert(tk.END, "✅ 链接可用\n\n")
                    else:
                        text_widget.insert(tk.END, "❌ 链接不可用\n\n")
                except Exception as e:
                    text_widget.insert(tk.END, f"❌ 测试失败: {str(e)}\n\n")
                
                text_widget.see(tk.END)
                test_window.update()
            
            text_widget.insert(tk.END, "测试完成！")
        
        # 在新线程中运行测试
        import threading
        test_thread = threading.Thread(target=run_tests, daemon=True)
        test_thread.start()

# 监控链接编辑对话框(监控链接编辑子界面)
class MonitorLinkDialog:
    def __init__(self, parent, title, link=None):
        self.parent = parent
        self.title = title
        self.link = link
        self.result = None
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x300")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self._build_ui()
        self._center_dialog()
        
        # 等待对话框关闭
        self.dialog.wait_window()
    
    def _center_dialog(self):
        """居中显示对话框"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
    
    def _build_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 表单字段
        fields = [
            ("名称:", "name", "entry"),
            ("URL:", "url", "entry"),
            ("检查间隔(秒):", "check_interval", "entry"),
            ("最大重试次数:", "max_retries", "entry")
        ]
        
        self.entries = {}
        
        for i, field_info in enumerate(fields):
            label_text, field_name, field_type = field_info
            
            # 标签
            label = ttk.Label(main_frame, text=label_text)
            label.grid(row=i, column=0, sticky="e", padx=5, pady=5)
            
            # 输入控件
            if field_type == "entry":
                entry = ttk.Entry(main_frame, width=40)
                entry.grid(row=i, column=1, sticky="ew", padx=5, pady=5)
                self.entries[field_name] = entry
        
        # 提示信息
        hint_label = ttk.Label(main_frame, text="提示：检查间隔留空表示使用随机5-8秒间隔", foreground="gray")
        hint_label.grid(row=len(fields), column=0, columnspan=2, pady=10)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=len(fields)+1, column=0, columnspan=2, pady=20)
        
        ttk.Button(button_frame, text="保存", command=self._save).pack(side="left", padx=5)
        ttk.Button(button_frame, text="取消", command=self._cancel).pack(side="left", padx=5)
        
        # 配置网格权重
        main_frame.columnconfigure(1, weight=1)
        
        # 填充现有数据
        if self.link:
            for field_name, entry in self.entries.items():
                if field_name in self.link:
                    value = self.link[field_name]
                    if field_name == 'check_interval' and value is None:
                        value = ''
                    entry.insert(0, str(value))
    
    def _save(self):
        """保存监控链接"""
        # 收集数据
        data = {}
        for field_name, entry in self.entries.items():
            value = entry.get().strip()
            
            if not value and field_name in ['name', 'url']:
                messagebox.showerror("错误", f"{field_name} 不能为空")
                return
            
            if field_name == 'check_interval':
                if value:
                    try:
                        data[field_name] = float(value)
                    except ValueError:
                        messagebox.showerror("错误", "检查间隔必须是数字")
                        return
                else:
                    data[field_name] = None
            elif field_name == 'max_retries':
                if value:
                    try:
                        data[field_name] = int(value)
                    except ValueError:
                        messagebox.showerror("错误", "最大重试次数必须是数字")
                        return
                else:
                    data[field_name] = 10
            else:
                data[field_name] = value
        
        self.result = data
        self.dialog.destroy()
    
    def _cancel(self):
        """取消操作"""
        self.dialog.destroy()

# # # # # # # # # # # # # # # # # # 监控链接管理结束(UI界面)  # # # # # # # # # # # # # # # # # #

# # # # # # # # # # # # # # # # # # 账号管理开始(UI界面)  # # # # # # # # # # # # # # # # # #

# 从数据库获取账号列表的函数
def get_accounts_from_database():
    """从数据库获取所有账号"""
    accounts = db.get_all_accounts()
    if not accounts:
        # 如果数据库为空，初始化默认账号
        default_accounts = [
            {
                "email": "<EMAIL>",
                "password": "12354jZHANJIALIN",
                "status": "ready",
                "url": "https://www.apple.com.cn/shop/buy-iphone/iphone-16-pro/MYTN3CH/A",
                "installment": "cmb",  # 招商银行
                "remark": "iPhone 7 妈妈手机"
            },
            {
                "email": "<EMAIL>",
                "password": "12354Jzhanjialin",
                "status": "ready",
                "url": "https://www.apple.com.cn/shop/buy-iphone/iphone-16-pro/MYTN3CH/A",
                "installment": "cmb",  # 招商银行
                "remark": "iPhone X 爸爸手机"
            },
            {
                "email": "<EMAIL>",
                "password": "12354Jzhanjialin",
                "status": "ready",
                "url": "https://www.apple.com.cn/shop/buy-iphone/iphone-16-pro/MYTN3CH/A",
                "installment": "cmb",  # 招商银行
                "remark": "18023594536"
            },
            {
                "email": "<EMAIL>",
                "password": "12354Jzhanjialin",
                "status": "ready",
                "url": "https://www.apple.com.cn/shop/buy-iphone/iphone-16-pro/MYTN3CH/A",
                "installment": "cmb",  # 招商银行
                "remark": "18023594536"
            },
            {
                "email": "<EMAIL>",
                "password": "12354Jzhanjialin",
                "status": "ready",
                "url": "https://www.apple.com.cn/shop/buy-iphone/iphone-16-pro/MYTN3CH/A",
                "installment": "ccb",  # 建设银行
                "remark": "18320473102"
            },
            {
                "email": "<EMAIL>",
                "password": "12354Jzhanjialin",
                "status": "ready",
                "url": "https://www.apple.com.cn/shop/buy-iphone/iphone-16-pro/MYTN3CH/A",
                "installment": "ccb",  # 建设银行
                "remark": "***********"
            },
            {
                "email": "<EMAIL>",
                "password": "12354Jzhanjialin",
                "status": "ready",
                "url": "https://www.apple.com.cn/shop/buy-iphone/iphone-16-pro/MYTN3CH/A",
                "installment": "ccb",  # 建设银行
                "remark": "***********"
            },
            {
                "email": "<EMAIL>",
                "password": "12354Jzhanjialin",
                "status": "ready",
                "url": "https://www.apple.com.cn/shop/buy-iphone/iphone-16-pro/MYTN3CH/A",
                "installment": "ccb",  # 建设银行
                "remark": "***********"
            } 
        ]
        
        # 导入默认账号到数据库
        for account in default_accounts:
            db.add_account(account)
        
        print("✅ 已初始化默认账号到数据库")
        return default_accounts
    
    return accounts

# 动态获取账号列表
def get_accounts():
    """获取当前账号列表"""
    return get_accounts_from_database()

# 分期方式转换函数(代码转换为中文)
def get_installment_display_name(installment_code):
    """将分期方式代码转换为中文显示名称"""
    installment_map = {
        'cmb': '招商银行',
        'ccb': '建设银行'
    }
    return installment_map.get(installment_code, installment_code)

# 分期方式转换函数(中文转换为代码)
def get_installment_code_from_name(display_name):
    """将中文显示名称转换为分期方式代码"""
    reverse_map = {
        '招商银行': 'cmb',
        '建设银行': 'ccb'
    }
    return reverse_map.get(display_name, display_name)

# Apple ID账号管理UI类
class DatabaseManagementUI:
    def __init__(self, parent_window=None):
        if parent_window:
            # 如果提供了父窗口，创建Toplevel窗口
            self.root = tk.Toplevel(parent_window)
            self.root.title("账号Apple ID账号管理")
            self.root.geometry("1000x600")
            self.root.transient(parent_window)  # 设置为子窗口
            self.root.grab_set()  # 模态窗口
            
            # 居中显示Apple ID账号管理窗口
            self.center_window()
        else:
            # 如果没有提供父窗口，创建独立的Tk窗口
            self.root = tk.Tk()
            self.root.title("账号Apple ID账号管理")
            self.root.geometry("1000x600")
            
            # 窗口居中显示
            self.center_window()
        
        self.db = db
        self.selected_account = None
        self._build_ui()
        self._refresh_accounts()
    
    def center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        window_width = self.root.winfo_width()
        window_height = self.root.winfo_height()
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
    
    def _build_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 标题
        #title = ttk.Label(main_frame, text="账号Apple ID账号管理", font=("Arial", 16, "bold"))
        #title.pack(pady=10)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x", pady=5)
        
        ttk.Button(button_frame, text="添加账号", command=self._show_add_dialog).pack(side="left", padx=5)
        ttk.Button(button_frame, text="编辑账号", command=self._show_edit_dialog).pack(side="left", padx=5)
        ttk.Button(button_frame, text="删除账号", command=self._delete_account).pack(side="left", padx=5)
        ttk.Button(button_frame, text="刷新列表", command=self._refresh_accounts).pack(side="left", padx=5)
        ttk.Button(button_frame, text="导入JSON", command=self._import_json).pack(side="left", padx=5)
        ttk.Button(button_frame, text="导出JSON", command=self._export_json).pack(side="left", padx=5)
        ttk.Button(button_frame, text="备份数据库", command=self._backup_database).pack(side="left", padx=5)
        ttk.Button(button_frame, text="查看日志", command=self._show_logs).pack(side="left", padx=5)
        ttk.Button(button_frame, text="监控链接管理", command=self._show_monitor_links).pack(side="left", padx=5)
        
        # 账号列表
        list_frame = ttk.Frame(main_frame)
        list_frame.pack(fill="both", expand=True, pady=10)
        
        # 创建Treeview
        columns = ("邮箱", "双重认证设备/手机号码", "密码", "分期方式", "URL", "收货信息", "最后使用", "创建时间")
        self.tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)
        
        # 设置列宽和标题
        column_widths = [200, 150, 120, 100, 300, 220, 150, 150]
        for i, col in enumerate(columns):
            self.tree.heading(col, text=col)
            self.tree.column(col, width=column_widths[i], anchor="center")
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 绑定选择事件
        self.tree.bind("<<TreeviewSelect>>", self._on_select)
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief="sunken")
        status_bar.pack(fill="x", pady=5)
    
    def _refresh_accounts(self):
        """刷新账号列表"""
        # 清空现有项目
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 获取所有账号
        accounts = self.db.get_all_accounts()
        
        # 添加到树形视图
        for account in accounts:
            last_used = account.get('last_used', '')
            if last_used:
                last_used = str(last_used)[:19]  # 截取到秒
            
            created_at = account.get('created_at', '')
            if created_at:
                created_at = str(created_at)[:19]
            
            # url列显示型号
            url_display = SKU_DISPLAY_MAP.get(account.get('url', ''), account.get('url', ''))
            # 收货信息展示
            shipping_info = account.get('shipping_info', {})
            shipping_str = f"{shipping_info.get('name','')}/{shipping_info.get('phone','')}/{shipping_info.get('address','')}" if shipping_info else ''
            self.tree.insert("", "end", values=(
                account['email'],
                account.get('remark', ''),
                account.get('password', ''),
                get_installment_display_name(account.get('installment', 'cmb')),
                url_display,
                shipping_str,
                last_used,
                created_at
            ), tags=(account['email'],))
        
        self.status_var.set(f"共 {len(accounts)} 个账号")
    
    def _on_select(self, event):
        """处理选择事件"""
        selection = self.tree.selection()
        if selection:
            item = selection[0]
            email = self.tree.item(item, "tags")[0]
            self.selected_account = self.db.get_account(email)
        else:
            self.selected_account = None
    
    def _show_add_dialog(self):
        """显示添加账号对话框"""
        dialog = AccountDialog(self.root, "添加账号", self.db)
        if dialog.result:
            self._refresh_accounts()
    
    def _show_edit_dialog(self):
        """显示编辑账号对话框"""
        if not self.selected_account:
            messagebox.showwarning("警告", "请先选择一个账号")
            return
        
        dialog = AccountDialog(self.root, "编辑账号", self.db, self.selected_account)
        if dialog.result:
            self._refresh_accounts()
    
    def _delete_account(self):
        """删除选中的账号"""
        if not self.selected_account:
            messagebox.showwarning("警告", "请先选择一个账号")
            return
        
        email = self.selected_account['email']
        if messagebox.askyesno("确认删除", f"确定要删除账号 {email} 吗？"):
            if self.db.delete_account(email):
                messagebox.showinfo("成功", f"账号 {email} 已删除")
                self._refresh_accounts()
            else:
                messagebox.showerror("错误", f"删除账号 {email} 失败")
    
    def _import_json(self):
        """导入JSON文件"""
        from tkinter import filedialog
        filename = filedialog.askopenfilename(
            title="选择JSON文件",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            success_count, total_count = self.db.import_from_json(filename)
            messagebox.showinfo("导入结果", f"成功导入 {success_count}/{total_count} 个账号")
            self._refresh_accounts()
    
    def _export_json(self):
        """导出JSON文件"""
        from tkinter import filedialog
        filename = filedialog.asksaveasfilename(
            title="保存JSON文件",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            if self.db.export_to_json(filename):
                messagebox.showinfo("成功", f"账号已导出到 {filename}")
            else:
                messagebox.showerror("错误", "导出失败")
    
    def _backup_database(self):
        """备份数据库"""
        from tkinter import filedialog
        filename = filedialog.asksaveasfilename(
            title="保存备份文件",
            defaultextension=".db",
            filetypes=[("Database files", "*.db"), ("All files", "*.*")]
        )
        if filename:
            if self.db.backup_database(filename):
                messagebox.showinfo("成功", f"数据库已备份到 {filename}")
            else:
                messagebox.showerror("错误", "备份失败")
    
    def _show_logs(self):
        """显示操作日志"""
        LogViewerDialog(self.root, self.db)
    
    def _show_monitor_links(self):
        """显示监控链接管理"""
        MonitorLinksDialog(self.root, self.db)
    
    def run(self):
        """运行UI"""
        self.root.mainloop()
# 账号编辑对话框
class AccountDialog:
    def __init__(self, parent, title, db, account=None):
        self.parent = parent
        self.title = title
        self.db = db
        self.account = account
        self.result = None
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x400")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self._build_ui()
        self._center_dialog()
        
        # 等待对话框关闭
        self.dialog.wait_window()
    
    def _center_dialog(self):
        """居中显示对话框"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
    
    def _build_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 表单字段
        fields = [
            ("邮箱:", "email", "entry"),
            ("密码:", "password", "entry"),
            ("双重认证设备/手机号码:", "remark", "entry"),
            ("状态:", "status", "combobox", ["ready", "locked", "used"]),
            ("分期方式:", "installment", "combobox", ["招商银行", "建设银行"]),
            ("型号:", "url", "combobox", SKU_DISPLAY_LIST),
            ("冷却时间(分钟):", "cooldown_minutes", "entry")
        ]
        
        self.entries = {}
        
        for i, field_info in enumerate(fields):
            label_text, field_name, field_type, *args = field_info
            label = ttk.Label(main_frame, text=label_text)
            label.grid(row=i, column=0, sticky="e", padx=5, pady=5)
            if field_type == "entry":
                if field_name == "password":
                    entry = ttk.Entry(main_frame, show="*")
                else:
                    entry = ttk.Entry(main_frame, width=40)
                entry.grid(row=i, column=1, sticky="ew", padx=5, pady=5)
                self.entries[field_name] = entry
            elif field_type == "combobox":
                combo = ttk.Combobox(main_frame, values=args[0], state="readonly", width=37)
                combo.grid(row=i, column=1, sticky="ew", padx=5, pady=5)
                self.entries[field_name] = combo
        
        # 新增收货信息区域
        shipping_label = ttk.Label(main_frame, text="收货信息:")
        shipping_label.grid(row=len(fields), column=0, sticky="ne", padx=5, pady=5)
        shipping_frame = ttk.Frame(main_frame)
        shipping_frame.grid(row=len(fields), column=1, sticky="ew", padx=5, pady=5)
        self.shipping_name = ttk.Entry(shipping_frame, width=12)
        self.shipping_name.grid(row=0, column=0, padx=2)
        self.shipping_name.insert(0, "收货人姓名")
        self.shipping_phone = ttk.Entry(shipping_frame, width=14)
        self.shipping_phone.grid(row=0, column=1, padx=2)
        self.shipping_phone.insert(0, "手机号")
        self.shipping_addr = ttk.Entry(shipping_frame, width=30)
        self.shipping_addr.grid(row=0, column=2, padx=2)
        self.shipping_addr.insert(0, "详细地址")
        
        # 填充已有数据
        if self.account:
            for field_name, entry in self.entries.items():
                if field_name in self.account:
                    if isinstance(entry, ttk.Combobox):
                        if field_name == "installment":
                            display_name = get_installment_display_name(self.account[field_name])
                            entry.set(display_name)
                        elif field_name == "url":
                            entry.set(SKU_DISPLAY_MAP.get(self.account[field_name], self.account[field_name]))
                        else:
                            entry.set(self.account[field_name])
                    else:
                        entry.insert(0, str(self.account[field_name]))
            # 收货信息
            shipping_info = self.account.get('shipping_info', {})
            if shipping_info:
                self.shipping_name.delete(0, tk.END)
                self.shipping_name.insert(0, shipping_info.get('name', ''))
                self.shipping_phone.delete(0, tk.END)
                self.shipping_phone.insert(0, shipping_info.get('phone', ''))
                self.shipping_addr.delete(0, tk.END)
                self.shipping_addr.insert(0, shipping_info.get('address', ''))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=len(fields)+1, column=0, columnspan=2, pady=20)
        
        ttk.Button(button_frame, text="保存", command=self._save).pack(side="left", padx=5)
        ttk.Button(button_frame, text="取消", command=self._cancel).pack(side="left", padx=5)
        
        # 配置网格权重
        main_frame.columnconfigure(1, weight=1)
    
    def _save(self):
        """保存账号"""
        # 收集数据
        data = {}
        for field_name, entry in self.entries.items():
            if isinstance(entry, ttk.Combobox):
                value = entry.get()
                # 如果是分期方式，将中文名称转换为代码
                if field_name == "installment":
                    value = get_installment_code_from_name(value)
                elif field_name == "url":
                    # 型号转url
                    value = SKU_REVERSE_MAP.get(value, value)
            else:
                value = entry.get().strip()
            
            if not value and field_name in ['email', 'password']:
                messagebox.showerror("错误", f"{field_name} 不能为空")
                return
            
            data[field_name] = value
        
        # 验证冷却时间
        try:
            data['cooldown_minutes'] = int(data['cooldown_minutes'])
        except ValueError:
            messagebox.showerror("错误", "冷却时间必须是数字")
            return
        
        # 收货信息
        shipping_info = {
            'name': self.shipping_name.get().strip(),
            'phone': self.shipping_phone.get().strip(),
            'address': self.shipping_addr.get().strip()
        }
        data['shipping_info'] = shipping_info
        
        # 保存到数据库
        if self.account:
            # 更新现有账号
            if self.db.update_account(self.account['email'], data):
                messagebox.showinfo("成功", "账号更新成功")
                self.result = True
                self.dialog.destroy()
            else:
                messagebox.showerror("错误", "更新失败")
        else:
            # 添加新账号
            if self.db.add_account(data):
                messagebox.showinfo("成功", "账号添加成功")
                self.result = True
                self.dialog.destroy()
            else:
                messagebox.showerror("错误", "添加失败")
    
    def _cancel(self):
        """取消操作"""
        self.dialog.destroy()

# 日志查看器对话框
class LogViewerDialog:
    def __init__(self, parent, db):
        self.parent = parent
        self.db = db
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("操作日志")
        self.dialog.geometry("800x500")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self._build_ui()
        self._center_dialog()
        self._refresh_logs()
    
    def _center_dialog(self):
        """居中显示对话框"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
    
    def _build_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 控制框架
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill="x", pady=5)
        
        ttk.Label(control_frame, text="账号:").pack(side="left")
        self.account_var = tk.StringVar(value="全部")
        account_combo = ttk.Combobox(control_frame, textvariable=self.account_var, state="readonly", width=30)
        account_combo.pack(side="left", padx=5)
        
        # 获取所有账号邮箱
        accounts = self.db.get_all_accounts()
        account_emails = ["全部"] + [acc['email'] for acc in accounts]
        account_combo['values'] = account_emails
        
        ttk.Button(control_frame, text="刷新", command=self._refresh_logs).pack(side="left", padx=5)
        ttk.Button(control_frame, text="清空日志", command=self._clear_logs).pack(side="left", padx=5)
        
        # 日志列表
        columns = ("时间", "账号", "操作类型", "操作详情", "状态")
        self.tree = ttk.Treeview(main_frame, columns=columns, show="headings", height=20)
        
        column_widths = [150, 200, 100, 300, 80]
        for i, col in enumerate(columns):
            self.tree.heading(col, text=col)
            self.tree.column(col, width=column_widths[i], anchor="center")
        
        # 滚动条
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def _refresh_logs(self):
        """刷新日志"""
        # 清空现有项目
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 获取日志
        account_email = self.account_var.get()
        if account_email == "全部":
            logs = self.db.get_operation_logs(limit=200)
        else:
            logs = self.db.get_operation_logs(account_email, limit=200)
        
        # 添加到树形视图
        for log in logs:
            created_at = str(log['created_at'])[:19] if log['created_at'] else ''
            status = "✅" if log['success'] else "❌"
            
            self.tree.insert("", "end", values=(
                created_at,
                log['email'],
                log['operation_type'],
                log['operation_details'],
                status
            ))
    
    def _clear_logs(self):
        """清空日志"""
        if messagebox.askyesno("确认", "确定要清空所有操作日志吗？"):
            # 这里可以添加清空日志的数据库操作
            messagebox.showinfo("提示", "日志清空功能待实现")

# # # # # # # # # # # # # # # # # # 账号管理结束(UI界面)  # # # # # # # # # # # # # # # # # #

# # # # # # # # # # # # # # # # # # 抢购流程开始(代码逻辑)  # # # # # # # # # # # # # # # # # #
# 抢购流程中页面状态检测、判断功能(共3步)
# 1.页面状态枚举,定义购买流程中常见的页面
class CheckoutPageState:
    """结账页面状态枚举"""
    # 购物袋页面
    SHOPPING_BAG = "shopping_bag"
    
    # 订单选项页面（选择配送时间）
    ORDER_OPTIONS = "order_options"
    
    # 送货详情页面（填写送货地址）
    SHIPPING_DETAILS = "shipping_details"
    
    # 付款详情页面（选择付款方式）
    PAYMENT_DETAILS = "payment_details"
    
    # 查看订单页面（最终确认）
    REVIEW_ORDER = "review_order"
    
    # 登录页面
    LOGIN = "login"
    
    # 未知页面
    UNKNOWN = "unknown"
    
    # 登陆-安全结账页面
    LOGIN_SECURE_CHECKOUT = "login_secure_checkout"

# 2.通过页面元素等判断当前页面的状态
def detect_page_state(driver, max_retries=3, retry_delay=2):
    """
    检测当前页面状态，支持重试机制
    返回: CheckoutPageState 枚举值
    """
    for attempt in range(max_retries):
        try:
            # 等待页面完全加载
            WebDriverWait(driver, 10).until(
                lambda d: d.execute_script("return document.readyState") == "complete"
            )
            
            current_title = driver.title
            current_url = driver.current_url
            
            print(f"🔄 页面状态检测 (尝试 {attempt + 1}/{max_retries}): 标题='{current_title}', URL='{current_url}'")
            
            # 1. 检测购物袋页面
            if "购物袋" in current_title or "bag" in current_url.lower():
                print("✅ 检测到购物袋页面")
                return CheckoutPageState.SHOPPING_BAG
            
            # 2. 检测订单选项页面（选择配送时间）
            if ("订单选项" in current_title or "order" in current_title.lower() or 
                "fulfillment" in current_url.lower()):
                # 进一步验证：检查是否有配送时间选择器
                if driver.find_elements(By.CSS_SELECTOR, "[data-autom='fulfillment-options']") or \
                   driver.find_elements(By.XPATH, "//h1[contains(text(), '你希望如何收到订单商品')]") or\
                   driver.find_elements(By.XPATH, "//button[contains(., '继续填写送货地址')]"):
                    print("✅ 检测到订单选项页面")
                    return CheckoutPageState.ORDER_OPTIONS
            
            # 3. 检测送货详情页面（填写送货地址）
            if ("送货详情" in current_title or "shipping" in current_title.lower() or 
                "shipping" in current_url.lower()):
                # 进一步验证：检查是否有送货地址表单
                if (driver.find_elements(By.XPATH, "//h1[contains(text(), '送货地址')]") or
                    driver.find_elements(By.CSS_SELECTOR, "[data-autom='shipping-address-form']") or
                    driver.find_elements(By.XPATH, "//h1[contains(text(), '你的送货地址是哪里')]") or
                    driver.find_elements(By.XPATH, "//button[contains(., '继续选择付款方式')]")):
                    print("✅ 检测到送货详情页面")
                    return CheckoutPageState.SHIPPING_DETAILS
            
            # 4. 检测付款详情页面（选择付款方式）
            if ("付款详情" in current_title or "payment" in current_title.lower() or 
                "billing" in current_title.lower() or "payment" in current_url.lower()):
                # 进一步验证：检查是否有付款方式选择器
                if (driver.find_elements(By.CSS_SELECTOR, "[data-autom='payment-methods']") or
                    driver.find_elements(By.CSS_SELECTOR, "div.rs-payment-options") or
                    driver.find_elements(By.XPATH, "//h1[contains(text(), '你希望如何付款')]") or
                    driver.find_elements(By.XPATH, "//button[contains(., '检查订单')]")):
                    print("✅ 检测到付款详情页面")
                    return CheckoutPageState.PAYMENT_DETAILS
            
            # 5. 检测查看订单页面（最终确认）
            if ("查看订单" in current_title or "review" in current_title.lower() or 
                "review" in current_url.lower()):
                # 进一步验证：检查是否有订单确认信息
                if (driver.find_elements(By.CSS_SELECTOR, "[data-autom='order-summary']") or
                driver.find_elements(By.XPATH, "//h1[contains(text(), '准备下单了吗')]") or
                    driver.find_elements(By.XPATH, "//button[contains(., '立即下单')]")):
                    print("✅ 检测到查看订单页面")
                    return CheckoutPageState.REVIEW_ORDER
            
            # 6. 检测登录页面
            if ("登录" in current_title or "sign in" in current_title.lower() or 
                "login" in current_url.lower()):
                print("✅ 检测到登录页面")
                return CheckoutPageState.LOGIN
            
            # 7. 通过URL进一步判断
            if "checkout" in current_url.lower():
                if "shipping" in current_url.lower():
                    print("✅ 通过URL检测到送货详情页面")
                    return CheckoutPageState.SHIPPING_DETAILS
                elif "payment" in current_url.lower() or "billing" in current_url.lower():
                    print("✅ 通过URL检测到付款详情页面")
                    return CheckoutPageState.PAYMENT_DETAILS
                elif "review" in current_url.lower():
                    print("✅ 通过URL检测到查看订单页面")
                    return CheckoutPageState.REVIEW_ORDER
                else:
                    print("✅ 通过URL检测到订单选项页面")
                    return CheckoutPageState.ORDER_OPTIONS
            
            # 8. 通过页面元素进一步判断
            try:
                # 检查是否有配送时间选择器
                if driver.find_elements(By.CSS_SELECTOR, "[data-autom='fulfillment-options']"):
                    print("✅ 通过元素检测到订单选项页面")
                    return CheckoutPageState.ORDER_OPTIONS
                
                # 检查是否有送货地址表单
                if (driver.find_elements(By.CSS_SELECTOR, "[data-autom='shipping-address-form']") or
                    driver.find_elements(By.XPATH, "//h1[contains(text(), '你的送货地址是哪里')]")):
                    print("✅ 通过元素检测到送货详情页面")
                    return CheckoutPageState.SHIPPING_DETAILS
                
                # 检查是否有付款方式选择器
                if (driver.find_elements(By.CSS_SELECTOR, "[data-autom='payment-methods']") or
                    driver.find_elements(By.CSS_SELECTOR, "div.rs-payment-options")):
                    print("✅ 通过元素检测到付款详情页面")
                    return CheckoutPageState.PAYMENT_DETAILS
                
                # 检查是否有订单确认信息
                if driver.find_elements(By.CSS_SELECTOR, "[data-autom='order-summary']"):
                    print("✅ 通过元素检测到查看订单页面")
                    return CheckoutPageState.REVIEW_ORDER
            except Exception as e:
                print(f"⚠️ 元素检测时发生错误: {str(e)}")
            
            # 如果所有检测都失败，等待后重试
            if attempt < max_retries - 1:
                print(f"⚠️ 页面状态检测失败，{retry_delay}秒后重试...")
                time.sleep(retry_delay)
            else:
                print(f"❌ 页面状态检测失败，已达到最大重试次数({max_retries})")
                return CheckoutPageState.UNKNOWN
                
        except Exception as e:
            print(f"❌ 页面状态检测时发生错误: {str(e)}")
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
            else:
                return CheckoutPageState.UNKNOWN
    
    return CheckoutPageState.UNKNOWN

# 3.等待页面状态跳转
def wait_for_page_state_change(driver, expected_state, timeout=30, check_interval=2):
    """
    等待页面状态变化到指定状态
    :param driver: WebDriver实例
    :param expected_state: 期望的页面状态
    :param timeout: 超时时间（秒）
    :param check_interval: 检查间隔（秒）
    :return: 是否成功到达期望状态
    """
    start_time = time.time()
    print(f"🔄 等待页面状态变化到: {expected_state}")
    
    while time.time() - start_time < timeout:
        current_state = detect_page_state(driver)
        print(f"🔄 当前页面状态: {current_state}")
        
        if current_state == expected_state:
            print(f"✅ 页面状态已成功变化到: {expected_state}")
            return True
        
        time.sleep(check_interval)
    
    print(f"❌ 等待页面状态变化超时，期望: {expected_state}")
    return False

# 抢购链接监控功能
class LinkMonitor:
    def __init__(self, target_url, check_interval=None, max_retries=10):
        """
        初始化链接监控器
        :param target_url: 目标网页链接
        :param check_interval: 检查间隔（秒），如果为None则使用随机5-8秒
        :param max_retries: 最大重试次数
        """
        self.target_url = target_url
        # 如果未指定检查间隔，使用随机5-8秒
        if check_interval is None:
            self.check_interval = random.uniform(5, 8)
        else:
            self.check_interval = check_interval
        self.max_retries = max_retries
        self.is_available = False
        self.last_check_time = None
        self.check_count = 0
        self.monitoring = False
        self.logger = self._setup_logger()
    
    def _setup_logger(self):
        """设置日志记录器"""
        logger = logging.getLogger('LinkMonitor')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            # 创建logs目录
            if not os.path.exists('logs'):
                os.makedirs('logs', exist_ok=True)
            
            # 文件处理器
            file_handler = logging.FileHandler(
                f'logs/link_monitor_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log',
                encoding='utf-8'
            )
            file_handler.setFormatter(logging.Formatter(
                '%(asctime)s [LinkMonitor] %(levelname)s: %(message)s'
            ))
            logger.addHandler(file_handler)
            
            # 控制台处理器
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(logging.Formatter(
                '%(asctime)s [LinkMonitor] %(levelname)s: %(message)s'
            ))
            logger.addHandler(console_handler)
        
        return logger
    
    def check_link_availability(self):
        """检查链接是否可用"""
        try:
            self.logger.info(f"🔄 正在检查链接: {self.target_url}")
            
            # 设置请求头，模拟真实浏览器
            headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }
            
            # 发送请求
            response = requests.get(
                self.target_url,
                headers=headers,
                timeout=10,
                allow_redirects=True
            )
            
            self.last_check_time = datetime.now()
            self.check_count += 1
            
            # 检查响应状态
            if response.status_code == 200:
                # 检查页面内容是否包含预期的关键词
                content = response.text.lower()
                expected_keywords = ['iphone', 'apple', 'shop', 'buy']
                
                if any(keyword in content for keyword in expected_keywords):
                    self.is_available = True
                    self.logger.info(f"✅ 链接可用！状态码: {response.status_code}, 检查次数: {self.check_count}")
                    add_log("系统", f"✅ 链接可用！状态码: {response.status_code}, 检查次数: {self.check_count} - {self.target_url}")
                    return True
                else:
                    self.logger.warning(f"⚠️ 链接返回200但内容不符合预期，检查次数: {self.check_count}")
                    return False
            else:
                self.logger.warning(f"⚠️ 链接不可用，状态码: {response.status_code}, 检查次数: {self.check_count}")
                return False
                
        except requests.exceptions.ConnectionError as e:
            self.logger.error(f"❌ 连接错误: {str(e)}, 检查次数: {self.check_count}")
            add_log("系统", f"❌ 连接错误: {str(e)}, 检查次数: {self.check_count} - {self.target_url}")
            return False
        except requests.exceptions.Timeout as e:
            self.logger.error(f"❌ 请求超时: {str(e)}, 检查次数: {self.check_count}")
            add_log("系统", f"❌ 请求超时: {str(e)}, 检查次数: {self.check_count} - {self.target_url}")
            return False
        except requests.exceptions.RequestException as e:
            self.logger.error(f"❌ 请求异常: {str(e)}, 检查次数: {self.check_count}")
            add_log("系统", f"❌ 请求异常: {str(e)}, 检查次数: {self.check_count} - {self.target_url}")
            return False
        except Exception as e:
            self.logger.error(f"❌ 未知错误: {str(e)}, 检查次数: {self.check_count}")
            add_log("系统", f"❌ 未知错误: {str(e)}, 检查次数: {self.check_count} - {self.target_url}")
            return False
    
    def start_monitoring(self):
        """开始监控链接"""
        # 检查是否已经在监控或链接已可用
        if self.monitoring:
            self.logger.info(f"⚠️ 监控器已在运行中，跳过重复启动")
            return self.is_available
        
        if self.is_available:
            self.logger.info(f"✅ 链接已可用，无需重新监控")
            return True
        
        # 重置状态
        self.check_count = 0
        self.monitoring = True
        self.logger.info(f"🚀 开始监控链接: {self.target_url}")
        self.logger.info(f"📊 检查间隔: 随机5-8秒, 最大重试次数: {self.max_retries}")
        
        # 添加UI日志
        add_log("系统", f"🚀 开始监控链接: {self.target_url}")
        
        try:
            while self.monitoring and self.check_count < self.max_retries:
                if self.check_link_availability():
                    self.logger.info("🎉 链接已上线，可以开始自动化流程！")
                    add_log("系统", f"🎉 链接已上线: {self.target_url}")
                    break
                else:
                    # 使用随机5-8秒间隔，但分段检查停止信号
                    random_interval = random.uniform(5, 8)
                    self.logger.info(f"⏰ {random_interval:.1f}秒后重新检查...")
                    
                    # 分段等待，每1秒检查一次停止信号
                    wait_time = 0
                    while wait_time < random_interval and self.monitoring:
                        time.sleep(min(1.0, random_interval - wait_time))
                        wait_time += 1.0
                    
                    # 如果被停止，立即退出
                    if not self.monitoring:
                        self.logger.info("🛑 收到停止信号，退出监控")
                        add_log("系统", f"🛑 监控已停止: {self.target_url}")
                        break
            
            if not self.is_available:
                self.logger.error(f"❌ 达到最大重试次数({self.max_retries})，链接仍未可用")
                add_log("系统", f"❌ 监控失败，链接仍未可用: {self.target_url}")
            
            return self.is_available
            
        finally:
            # 确保监控状态被正确设置
            self.monitoring = False
            final_status = '可用' if self.is_available else '不可用'
            self.logger.info(f"🛑 监控器已停止，最终状态: {final_status}")
            add_log("系统", f"🛑 监控器已停止，最终状态: {final_status} - {self.target_url}")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        self.logger.info("🛑 停止链接监控")
    
    def get_status(self):
        """获取监控状态"""
        return {
            'target_url': self.target_url,
            'is_available': self.is_available,
            'check_count': self.check_count,
            'last_check_time': self.last_check_time,
            'monitoring': self.monitoring
        }

# 智能链接检测器
class SmartLinkDetector:
    def __init__(self):
        self.config_file = 'link_monitor_config.json'
        self.monitors = {}
        self.load_config()
        # 根据配置文件创建监控器实例
        self._create_monitors_from_config()
    
    def _create_monitors_from_config(self):
        """根据配置文件创建监控器实例"""
        for link_config in self.config.get('monitored_links', []):
            name = link_config['name']
            url = link_config['url']
            check_interval = link_config.get('check_interval', self.config.get('default_check_interval'))
            max_retries = link_config.get('max_retries', self.config.get('default_max_retries'))
            
            monitor = LinkMonitor(url, check_interval, max_retries)
            self.monitors[name] = monitor
            print(f"✅ 从配置文件创建监控器: {name} -> {url}")
    
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            else:
                self.config = {
                    'default_check_interval': None,  # 使用随机5-8秒间隔
                    'default_max_retries': 10,
                    'monitored_links': []
                }
                self.save_config()
        except Exception as e:
            print(f"❌ 加载配置文件失败: {str(e)}")
            self.config = {
                'default_check_interval': None,  # 使用随机5-8秒间隔
                'default_max_retries': 10,
                'monitored_links': []
            }
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"❌ 保存配置文件失败: {str(e)}")
    
    def add_link(self, url, name=None, check_interval=None, max_retries=None):
        """添加监控链接"""
        if name is None:
            name = f"link_{len(self.monitors)}"
        
        # 如果未指定检查间隔，使用配置中的默认值（None表示随机5-8秒）
        if check_interval is None:
            check_interval = self.config['default_check_interval']
        
        if max_retries is None:
            max_retries = self.config['default_max_retries']
        
        monitor = LinkMonitor(url, check_interval, max_retries)
        self.monitors[name] = monitor
        
        # 保存到配置
        self.config['monitored_links'].append({
            'name': name,
            'url': url,
            'check_interval': check_interval,
            'max_retries': max_retries
        })
        self.save_config()
        
        print(f"✅ 已添加监控链接: {name} -> {url}")
        return monitor
    
    def start_all_monitors(self):
        """启动所有监控器"""
        results = {}
        print(f"🔄 启动 {len(self.monitors)} 个监控器...")
        for name, monitor in self.monitors.items():
            # 在新线程中运行监控
            thread = threading.Thread(target=self._run_monitor, args=(name, monitor))
            thread.daemon = True
            thread.start()
            results[name] = monitor
        
        return results
    
    def stop_all_monitors(self):
        """停止所有监控器"""
        # 检查是否已经停止
        if not any(monitor.monitoring for monitor in self.monitors.values()):
            return
            
        print("🛑 正在停止所有监控器...")
        for name, monitor in self.monitors.items():
            if monitor.monitoring:  # 只停止正在运行的监控器
                monitor.stop_monitoring()
        print("✅ 所有监控器已停止")
    
    def _run_monitor(self, name, monitor):
        """在独立线程中运行监控器"""
        try:
            add_log("系统", f"🚀 开始监控链接: {name}")
            success = monitor.start_monitoring()
            if success:
                add_log("系统", f"🎉 监控器 {name} 检测到链接可用！")
                print(f"🎉 监控器 {name} 检测到链接可用！")
                # 注意：停止监控器的逻辑已经在 wait_for_any_link 中处理
                # 这里不需要重复调用 stop_all_monitors()
            else:
                add_log("系统", f"❌ 监控器 {name} 未检测到链接可用")
                print(f"❌ 监控器 {name} 未检测到链接可用")
        except Exception as e:
            error_msg = f"❌ 监控器 {name} 运行出错: {str(e)}"
            add_log("系统", error_msg)
            print(error_msg)
    
    def wait_for_any_link(self, timeout=None):
        """等待任意一个链接可用"""
        start_time = time.time()
        stopped = False  # 标记是否已经停止监控器
        
        while True:
            # 检查是否有链接可用
            for name, monitor in self.monitors.items():
                if monitor.is_available:
                    print(f"🎉 检测到可用链接: {name}")
                    # 只停止一次
                    if not stopped:
                        self.stop_all_monitors()
                        stopped = True
                    return name, monitor
            
            # 检查超时
            if timeout and (time.time() - start_time) > timeout:
                print(f"⏰ 等待超时 ({timeout}秒)")
                if not stopped:
                    self.stop_all_monitors()
                return None, None
            
            # 等待一段时间后重新检查
            time.sleep(5)
    
    def get_all_status(self):
        """获取所有监控器状态"""
        status = {}
        for name, monitor in self.monitors.items():
            status[name] = monitor.get_status()
        return status
    
    def get_available_links(self):
        """获取所有可用的链接"""
        available_links = []
        for name, monitor in self.monitors.items():
            if monitor.is_available:
                available_links.append({
                    'name': name,
                    'url': monitor.target_url,
                    'monitor': monitor
                })
        return available_links

# 通用等待函数
def wait_for_page_load(driver, timeout=10):
    """
    等待页面完全加载，包括DOM、图片、脚本等
    """
    try:
        # 等待DOM加载完成
        WebDriverWait(driver, timeout).until(
            lambda d: d.execute_script("return document.readyState") == "complete"
        )
        
        # 等待jQuery加载完成（如果存在）
        #try:
        #    WebDriverWait(driver, 5).until(
        #        lambda d: d.execute_script("return typeof jQuery !== 'undefined' && jQuery.active === 0")
        #    )
        #except:
        #    pass  # jQuery可能不存在，忽略错误
        
        # 等待所有图片加载完成
        #try:
        #    WebDriverWait(driver, 5).until(
        #        lambda d: d.execute_script("""
        #            return Array.from(document.images).every(img => img.complete);
        #        """)
        #    )
        #except:
        #   pass  # 图片加载可能超时，忽略错误
        
        # 智能等待页面稳定（检测AJAX请求完成）
        try:
            WebDriverWait(driver, 3).until(
                lambda d: d.execute_script("""
                    return (typeof jQuery === 'undefined' || jQuery.active === 0) &&
                           (typeof window.fetch === 'undefined' ||
                            !window.performance.getEntriesByType('navigation')[0].loadEventEnd === 0);
                """)
            )
        except:
            # 如果检测失败，使用最小等待时间
            time.sleep(0.5)
        
        print(f"✅ 页面加载完成，标题: {driver.title}")
        return True
        
    except Exception as e:
        print(f"⚠️ 页面加载等待超时或出错: {str(e)}")
        return False

# 等待元素可点击
def wait_for_element_clickable(driver, locator, timeout=10):
    """等待元素可点击"""
    return WebDriverWait(driver, timeout).until(
        EC.element_to_be_clickable(locator)
    )

# 等待元素出现
def wait_for_element_present(driver, locator, timeout=10):
    """等待元素出现"""
    return WebDriverWait(driver, timeout).until(
        EC.presence_of_element_located(locator)
    )

# 等待元素可见
def wait_for_element_visible(driver, locator, timeout=10):
    """等待元素可见"""
    return WebDriverWait(driver, timeout).until(
        EC.visibility_of_element_located(locator)
    )

# 等待 URL
def wait_for_url_contains(driver, url_part, timeout=10):
    """等待URL包含指定部分"""
    try:
        WebDriverWait(driver, timeout).until(
            lambda d: url_part in d.current_url
        )
        return True
    except TimeoutException:
        return False

# 读取代理池
def load_proxies(proxy_file):
    """从文件中加载代理列表"""
    import urllib.parse
    proxies = []
    try:
        with open(proxy_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    parts = line.split(':')
                    if len(parts) >= 2:
                        host = parts[0]
                        port = parts[1]
                        username = urllib.parse.unquote(parts[2]) if len(parts) > 2 else None
                        password = urllib.parse.unquote(parts[3]) if len(parts) > 3 else None
                        proxies.append({
                            'host': host,
                            'port': port,
                            'username': username,
                            'password': password
                        })
    except Exception as e:
        logging.error(f"加载代理文件出错: {str(e)}")
    return proxies

def test_proxy(proxy_host, proxy_port, proxy_username=None, proxy_password=None, timeout=10):
    """测试代理是否可用"""
    from urllib.parse import quote
    
    proxy_url = f"http://{proxy_host}:{proxy_port}"
    auth = None
    if proxy_username and proxy_password:
        # URL编码用户名和密码，处理特殊字符如+号
        encoded_username = quote(proxy_username, safe='')
        encoded_password = quote(proxy_password, safe='')
        auth = f"{encoded_username}:{encoded_password}"
        proxy_url = f"http://{auth}@{proxy_host}:{proxy_port}"
    
    proxies = {
        "http": proxy_url,
        "https": proxy_url
    }
    
    try:
        start_time = time.time()
        response = requests.get("https://www.apple.com.cn", 
                               proxies=proxies, 
                               timeout=timeout)
        elapsed = time.time() - start_time
        
        if response.status_code == 200:
            return True, elapsed
        return False, 0
    except Exception as e:
        logging.debug(f"代理测试失败: {proxy_url}, 错误: {str(e)}")
        return False, 0

def test_proxy_with_browser(proxy_host, proxy_port, proxy_username=None, proxy_password=None, timeout=15):
    """使用Chrome浏览器测试代理是否可用"""
    # 分离代理URL和认证信息，避免URL中包含认证信息
    proxy_url = f"http://{proxy_host}:{proxy_port}"
    
    # 记录认证信息便于调试
    auth_message = ""
    if proxy_username and proxy_password:
        auth_message = f" (使用认证用户: {proxy_username})"

    driver = None
    try:
        # 创建与实际运行完全一致的Chrome配置
        chrome_options = Options()
        chrome_options.add_argument(f'--proxy-server={proxy_url}')
        chrome_options.add_argument("--headless=new")   # 无头模式
        chrome_options.add_argument("--window-size=1200,2000")   # 窗口大小
        chrome_options.add_argument("--disable-gpu")    # 禁用GPU硬件加速
        chrome_options.add_argument("--no-sandbox")     # 禁用Chrome沙盒安全机制
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")  # 隐藏自动化特征
        chrome_options.add_argument("--disable-extensions")  # 禁用扩展，防止干扰代理设置
        chrome_options.add_argument("--ignore-certificate-errors")  # 忽略证书错误
        chrome_options.add_argument("--allow-insecure-localhost")  # 允许不安全的本地连接
        chrome_options.add_argument("user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
        
        # 代理身份验证设置 - 使用CDP命令设置认证
        # 这比在URL中包含认证信息更可靠
        proxy_auth_capabilities = {}
        if proxy_username and proxy_password:
            proxy_auth_capabilities = {
                'proxyServer': proxy_url,
                'proxyUsername': proxy_username,
                'proxyPassword': proxy_password
            }
            
        # 设置代理环境变量，某些情况下这可以解决代理问题
        if proxy_username and proxy_password:
            # URL编码用户名和密码
            from urllib.parse import quote
            encoded_username = quote(proxy_username, safe='')
            encoded_password = quote(proxy_password, safe='')
            auth_proxy_url = f"http://{encoded_username}:{encoded_password}@{proxy_host}:{proxy_port}"
            os.environ['HTTP_PROXY'] = auth_proxy_url
            os.environ['HTTPS_PROXY'] = auth_proxy_url
        else:
            os.environ['HTTP_PROXY'] = proxy_url
            os.environ['HTTPS_PROXY'] = proxy_url
        
        # 使用与实际运行一致的临时用户数据目录
        # 通常真实运行时每个账号会有单独目录，这里创建一个临时的测试目录
        temp_profile = os.path.join(os.getcwd(), f"proxy_test_profile")
        if os.path.exists(temp_profile):
            try:
                import shutil
                shutil.rmtree(temp_profile, ignore_errors=True)
            except:
                pass
        os.makedirs(temp_profile, exist_ok=True)
        chrome_options.add_argument(f"--user-data-dir={temp_profile}")
        
        # 初始化 WebDriver
        service = Service("/usr/local/bin/chromedriver")
        
        # 测量启动时间
        start_time = time.time()
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # 设置页面加载超时
        driver.set_page_load_timeout(timeout)
        
        # 隐藏自动化特征
        driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
            "source": """
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined
                })
            """
        })
        
        # 如果有代理认证信息，使用CDP命令设置
        if proxy_username and proxy_password:
            import base64
            try:
                # 先启用 Network
                driver.execute_cdp_cmd('Network.enable', {})
                
                # 设置代理认证头
                # 注意：这里不需要URL编码，因为base64编码会处理特殊字符
                auth_string = f"{proxy_username}:{proxy_password}"
                encoded_auth = base64.b64encode(auth_string.encode('utf-8')).decode('utf-8')
                driver.execute_cdp_cmd('Network.setExtraHTTPHeaders', {
                    'headers': {
                        'Proxy-Authorization': f'Basic {encoded_auth}'
                    }
                })
                logging.debug(f"测试代理认证设置完成 (用户名: {proxy_username}, 编码后: {encoded_auth[:20]}...)")
            except Exception as e:
                logging.debug(f"设置测试代理认证失败: {str(e)}")
        
        # 访问测试网站，与实际运行使用同一个URL
        driver.get("https://www.apple.com.cn/shop/buy-iphone/iphone-16-pro/MYTN3CH/A")
        
        # 等待页面加载并检查是否有错误
        time.sleep(3)  # 给错误页面一点时间加载
        
        # 获取页面源码和标题
        page_source = driver.page_source
        page_title = driver.title
        
        # 检查常见的代理错误
        error_messages = [
            "ERR_NO_SUPPORTED_PROXIES",
            "ERR_PROXY_CONNECTION_FAILED",
            "ERR_TUNNEL_CONNECTION_FAILED",
            "ERR_HTTPS_PROXY_TUNNEL_RESPONSE",
            "ERR_CACHE_ACCESS_DENIED",
            "Cache Access Denied",
            "Authentication Required",
            "Auth ErrMsg",
            "No such user",
            "无法访问此网站",
            "代理服务器连接失败",
            "需要身份验证",
            "请先验证您的身份"
        ]
        
        for error in error_messages:
            if error in page_source:
                # 保存错误截图便于分析
                try:
                    error_id = f"proxy_error_{proxy_host}_{proxy_port}_{int(time.time())}"
                    screenshot_path = f"{error_id}.png"
                    driver.save_screenshot(screenshot_path)
                    logging.info(f"代理浏览器测试失败: {proxy_url}, 发现错误: {error}, 已保存截图: {screenshot_path}")
                except Exception as e:
                    logging.error(f"保存错误截图失败: {str(e)}")
                
                return False, 0
                
        # 检查页面是否加载了有效内容
        valid_content = [
            "Apple Store",
            "iPhone",
            "购买",
            "无折抵换购"
        ]
        
        # 至少应该包含其中一个有效内容标记
        has_valid_content = any(content in page_source for content in valid_content)
        if not has_valid_content:
            logging.info(f"代理浏览器测试失败: {proxy_url}, 页面内容无效")
            return False, 0
        
        # 计算总耗时
        elapsed = time.time() - start_time
        
        # 测试成功
        logging.info(f"代理浏览器测试成功: {proxy_url}, 页面标题: {page_title}, 耗时: {elapsed:.2f}秒")
        return True, elapsed
        
    except Exception as e:
        logging.debug(f"代理浏览器测试失败: {proxy_url}, 错误: {str(e)}")
        return False, 0
        
    finally:
        # 关闭浏览器
        if driver:
            try:
                driver.quit()
            except:
                pass
            
        # 清理临时用户数据目录
        try:
            if temp_profile and os.path.exists(temp_profile):
                import shutil
                shutil.rmtree(temp_profile, ignore_errors=True)
                logging.debug(f"已清理测试用的临时浏览器配置目录: {temp_profile}")
        except Exception as e:
            logging.debug(f"清理临时目录失败: {str(e)}")

def get_random_proxy(proxies):
    """获取随机代理"""
    if not proxies:
        return None
    return random.choice(proxies)

class ProxyPool:
    """代理池管理类"""
    def __init__(self, proxy_file=None, auto_test=False):
        self.proxies = []
        self.working_proxies = []
        self.proxy_file = proxy_file or "proxies.txt"
        self.proxy_in_use = {}  # 跟踪哪些代理正在被哪些账号使用
        self.lock = threading.RLock()
        self._load_proxies()
        
        # 自动测试代理
        if auto_test and self.proxies:
            logging.info(f"启动时自动测试代理池中的 {len(self.proxies)} 个代理")
            # 使用浏览器测试，保证与实际运行环境一致
            self.test_all_proxies(max_workers=3, use_browser=True)
    
    def _load_proxies(self):
        """加载代理列表"""
        with self.lock:
            self.proxies = load_proxies(self.proxy_file)
            logging.info(f"已加载 {len(self.proxies)} 个代理")
            # 添加调试信息
            for i, proxy in enumerate(self.proxies):
                logging.debug(f"代理 {i+1}: {proxy['host']}:{proxy['port']} 用户名: {proxy.get('username', 'None')} 密码: {proxy.get('password', 'None')}")
    
    def reload_proxies(self):
        """重新加载代理列表"""
        self._load_proxies()
        return len(self.proxies)
    
    def test_all_proxies(self, max_workers=10, use_browser=True):
        """测试所有代理并更新可用代理列表
        
        Args:
            max_workers: 最大并发测试数量
            use_browser: 是否使用浏览器进行测试
        """
        with self.lock:
            self.working_proxies = []
            
            def test_single_proxy(proxy):
                host = proxy['host']
                port = proxy['port']
                username = proxy.get('username')
                password = proxy.get('password')
                
                if use_browser:
                    # 使用浏览器测试（与实际运行环境相同）
                    working, speed = test_proxy_with_browser(host, port, username, password)
                else:
                    # 使用简单HTTP请求测试
                    working, speed = test_proxy(host, port, username, password)
                
                proxy['working'] = working  # 新增：同步可用状态
                if working:
                    proxy['speed'] = speed
                    proxy['last_test'] = time.time()
                    return proxy
                return None
            
            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                results = list(executor.map(test_single_proxy, self.proxies))
            
            self.working_proxies = [p for p in results if p is not None]
            self.working_proxies.sort(key=lambda x: x.get('speed', float('inf')))
            
            logging.info(f"测试完成，{len(self.working_proxies)}/{len(self.proxies)} 个代理可用")
            return self.working_proxies
    
    def get_proxy(self, account_email=None, prefer_unused=True):
        """获取一个可用代理
        
        Args:
            account_email: 账号邮箱，用于跟踪哪个账号在使用哪个代理
            prefer_unused: 是否优先选择未使用的代理
        
        Returns:
            dict: 代理信息字典，包含host, port, username, password
        """
        with self.lock:
            if not self.working_proxies:
                # 如果没有测试过代理，或者没有可用代理，则先测试
                if not self.proxies:
                    self._load_proxies()
                self.test_all_proxies()
                
                # 如果还是没有可用代理，则直接从全部代理中随机选择
                if not self.working_proxies and self.proxies:
                    selected_proxy = get_random_proxy(self.proxies)
                    if account_email:
                        self.proxy_in_use[account_email] = selected_proxy
                    return selected_proxy
                elif not self.working_proxies:
                    return None
            
            # 优先选择未使用的代理
            if prefer_unused and account_email:
                unused_proxies = [p for p in self.working_proxies 
                                if p not in self.proxy_in_use.values()]
                if unused_proxies:
                    selected_proxy = get_random_proxy(unused_proxies)
                    self.proxy_in_use[account_email] = selected_proxy
                    return selected_proxy
            
            # 如果没有未使用的代理或不需要考虑使用情况，则从所有可用代理中选择
            selected_proxy = get_random_proxy(self.working_proxies)
            if account_email:
                self.proxy_in_use[account_email] = selected_proxy
            return selected_proxy
    
    def release_proxy(self, account_email):
        """释放账号使用的代理"""
        with self.lock:
            if account_email in self.proxy_in_use:
                del self.proxy_in_use[account_email]
    
    def get_proxy_url(self, proxy):
        """获取代理URL字符串，用于Selenium配置"""
        if not proxy:
            return None
        
        host = proxy['host']
        port = proxy['port']
        username = proxy.get('username')
        password = proxy.get('password')
        
        if username and password:
            return f"http://{username}:{password}@{host}:{port}"
        return f"http://{host}:{port}"
    
    def get_status(self):
        """获取代理池状态"""
        with self.lock:
            return {
                'total': len(self.proxies),
                'working': len(self.working_proxies),
                'in_use': len(self.proxy_in_use),
                'usage': {email: proxy['host'] for email, proxy in self.proxy_in_use.items()}
            }

# 创建全局代理池实例
proxy_pool = ProxyPool(auto_test=False)  # 禁用自动测试功能
# 强制重新加载代理配置
proxy_pool.reload_proxies()

# 创建线程安全的日志队列
log_queue = collections.deque(maxlen=1000)  # 保存最近1000条日志
log_lock = threading.Lock()

class SoundManager:
    """音效管理器"""
    
    def __init__(self):
        self.enabled = True
        self._setup_sound_system()
    
    def _setup_sound_system(self):
        """设置音效系统"""
        try:
            # 检测操作系统
            system = platform.system()
            if system == "Darwin":  # macOS
                self._play_sound = self._play_sound_macos
            elif system == "Windows":
                self._play_sound = self._play_sound_windows
            else:  # Linux
                self._play_sound = self._play_sound_linux
        except Exception as e:
            print(f"⚠️ 音效系统初始化失败: {e}")
            self._play_sound = self._play_sound_dummy
    
    def _play_sound_macos(self, sound_type="success"):
        """macOS音效播放"""
        try:
            if sound_type == "success":
                os.system("afplay /System/Library/Sounds/Glass.aiff")
            elif sound_type == "alert":
                os.system("afplay /System/Library/Sounds/Ping.aiff")
            elif sound_type == "notification":
                os.system("afplay /System/Library/Sounds/Notification.aiff")
        except Exception as e:
            print(f"⚠️ macOS音效播放失败: {e}")
    
    def _play_sound_windows(self, sound_type="success"):
        """Windows音效播放"""
        try:
            import winsound
            if sound_type == "success":
                winsound.MessageBeep(winsound.MB_ICONASTERISK)
            elif sound_type == "alert":
                winsound.MessageBeep(winsound.MB_ICONEXCLAMATION)
            elif sound_type == "notification":
                winsound.MessageBeep(winsound.MB_ICONINFORMATION)
        except Exception as e:
            print(f"⚠️ Windows音效播放失败: {e}")
    
    def _play_sound_linux(self, sound_type="success"):
        """Linux音效播放"""
        try:
            if sound_type == "success":
                os.system("paplay /usr/share/sounds/freedesktop/stereo/complete.oga 2>/dev/null || echo -e '\a'")
            elif sound_type == "alert":
                os.system("paplay /usr/share/sounds/freedesktop/stereo/message.oga 2>/dev/null || echo -e '\a'")
            elif sound_type == "notification":
                os.system("paplay /usr/share/sounds/freedesktop/stereo/notification.oga 2>/dev/null || echo -e '\a'")
        except Exception as e:
            print(f"⚠️ Linux音效播放失败: {e}")
    
    def _play_sound_dummy(self, sound_type="success"):
        """虚拟音效播放（当系统不支持时）"""
        print(f"🔊 音效播放: {sound_type}")
    
    def play_success_sound(self):
        """播放成功音效"""
        if self.enabled:
            threading.Thread(target=self._play_sound, args=("success",), daemon=True).start()
    
    def play_alert_sound(self):
        """播放警告音效"""
        if self.enabled:
            threading.Thread(target=self._play_sound, args=("alert",), daemon=True).start()
    
    def play_notification_sound(self):
        """播放通知音效"""
        if self.enabled:
            threading.Thread(target=self._play_sound, args=("notification",), daemon=True).start()
    
    def toggle_sound(self, enabled=None):
        """切换音效开关"""
        if enabled is not None:
            self.enabled = enabled
        else:
            self.enabled = not self.enabled
        return self.enabled

# 创建全局音效管理器
sound_manager = SoundManager()

def add_log(account_email, message):
    """添加日志到全局队列"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    log_entry = {
        'timestamp': timestamp,
        'account': account_email,
        'message': message
    }
    with log_lock:
        log_queue.append(log_entry)

# 创建全局的验证码输入队列
verification_queues = {}

# 双重认证2FA 输入界面
class VerificationCodeUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("双重认证验证码输入")
        self.root.geometry("500x400")

        # 窗口居中显示
        self.center_window()

        # 创建标题标签
        title_label = ttk.Label(self.root, text="请输入对应账号的验证码", font=("Arial", 12, "bold"))
        title_label.pack(pady=10)

        # 提示标签（无输入框时显示）
        self.hint_label = ttk.Label(self.root, text="暂无账号需要输入验证码", foreground="gray")
        self.hint_label.pack(pady=10)

        # 账号输入区域容器
        self.input_area = ttk.Frame(self.root)
        self.input_area.pack(fill="both", expand=True)

        self.frames = {}
        self.entries = {}
        self.submit_buttons = {}
        self.status_labels = {}

    def center_window(self):
        """将窗口居中显示"""
        # 更新窗口信息，确保获取正确的尺寸
        self.root.update_idletasks()
        
        # 获取屏幕尺寸
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        # 获取窗口尺寸
        window_width = self.root.winfo_width()
        window_height = self.root.winfo_height()
        
        # 计算居中位置
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        
        # 设置窗口位置
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")

    def show_verification_input(self, account_email, remark=None):
        # 如果已存在则不重复添加
        if account_email in self.frames:
            self.frames[account_email].pack(fill="x", padx=10, pady=5)
            self.status_labels[account_email].config(text="⚠️ 需要输入验证码", foreground="orange")
            self.entries[account_email].focus_set()
            self.hint_label.pack_forget()
            return
        # 获取双重认证设备/手机号码
        remark_text = f"（{remark}）" if remark else ''
        # 创建主框架
        frame = ttk.LabelFrame(self.input_area, text=f"账号: {account_email}{remark_text}")
        frame.pack(padx=10, pady=5, fill="x")
        # 创建验证码输入区域
        input_frame = ttk.Frame(frame)
        input_frame.pack(fill="x", padx=5, pady=5)
        # 创建验证码输入框
        entry = ttk.Entry(input_frame, width=20, font=("Arial", 12))
        entry.pack(side="left", padx=5)
        # 创建提交按钮
        submit_btn = ttk.Button(input_frame, text="提交验证码", 
                              command=lambda acc=account_email, ent=entry: self.submit_code(acc, ent))
        submit_btn.pack(side="left", padx=5)
        # 创建状态标签
        status_label = ttk.Label(frame, text="等待验证码...", foreground="blue")
        status_label.pack(pady=5)
        # 创建提示标签
        hint_label = ttk.Label(frame, text="请输入6位数字验证码", foreground="gray")
        hint_label.pack(pady=2)
        self.frames[account_email] = frame
        self.entries[account_email] = entry
        self.submit_buttons[account_email] = submit_btn
        self.status_labels[account_email] = status_label
        # 创建验证码队列（如果未创建）
        if account_email not in verification_queues:
            verification_queues[account_email] = queue.Queue()
        self.hint_label.pack_forget()
        entry.focus_set()
        
        # 显示验证码窗口
        self.root.deiconify()  # 显示窗口
        self.root.lift()       # 将窗口置于前台
        self.root.focus_force()  # 强制获取焦点

    def hide_verification_input(self, account_email):
        if account_email in self.frames:
            self.frames[account_email].pack_forget()
        # 如果所有输入框都隐藏，则显示提示
        if not any(frame.winfo_ismapped() for frame in self.frames.values()):
            self.hint_label.pack(pady=10)

    def submit_code(self, account_email, entry):
        code = entry.get().strip()
        if len(code) == 6 and code.isdigit():
            verification_queues[account_email].put(code)
            entry.delete(0, tk.END)
            self.status_labels[account_email].config(
                text="✅ 验证码已提交，正在处理...",
                foreground="green"
            )
            # 可选：自动隐藏输入框
            self.hide_verification_input(account_email)
        else:
            self.status_labels[account_email].config(
                text="❌ 请输入6位数字验证码",
                foreground="red"
            )

    def highlight_account(self, account_email):
        for email, frame in self.frames.items():
            if email == account_email:
                frame.configure(style='Highlight.TLabelframe')
                self.status_labels[email].config(
                    text="⚠️ 需要输入验证码",
                    foreground="orange"
                )
                self.entries[email].focus_set()
            else:
                frame.configure(style='TLabelframe')

    def update(self):
        self.root.update()

    def run(self):
        style = ttk.Style()
        style.configure('Highlight.TLabelframe', borderwidth=2, relief="solid")
        style.configure('Highlight.TLabelframe.Label', foreground='red')
        # 注意：不再调用mainloop，因为现在作为子窗口运行

# 双重认证2FA 验证码超时设定
def get_verification_code(account_email):
    """获取指定账号的验证码（动态显示输入框）"""
    # 确保队列已创建
    if account_email not in verification_queues:
        verification_queues[account_email] = queue.Queue()
    # 获取账号双重认证设备/手机号码
    remark = None
    for acc in get_accounts():
        if acc["email"] == account_email:
            remark = acc.get("remark", None)
            break
    
    # 使用after方法在主线程中安全地显示验证码输入框
    def show_input():
        try:
            ui.verification_ui.show_verification_input(account_email, remark)
            ui.verification_ui.highlight_account(account_email)
        except Exception as e:
            print(f"显示验证码输入框时出错: {e}")
    
    # 在主线程中执行
    if hasattr(ui, 'root') and ui.root:
        ui.root.after(0, show_input)
    
    try:
        return verification_queues[account_email].get(timeout=300)  # 5分钟超时
    except queue.Empty:
        return None

# 检测账号状态
def get_available_account():
    """获取可用的账号"""
    current_time = datetime.now()
    
    # 从数据库获取所有账号
    accounts = get_accounts()
    
    for account in accounts:
        # 检查账号状态
        if account["status"] == "locked":
            continue
            
        # 检查冷却时间
        if account["last_used"] is not None:
            try:
                last_used = datetime.fromisoformat(str(account["last_used"]).replace('Z', '+00:00'))
                cooldown_minutes = account.get("cooldown_minutes", 30)
                cooldown_time = last_used + timedelta(minutes=cooldown_minutes)
                if current_time < cooldown_time:
                    continue
            except:
                # 如果时间解析失败，跳过这个账号
                continue
                
        return account
    
    return None

# 检测账号状态
def update_account_status(account, success=True):
    """更新账号状态"""
    if success:
        db.update_account_status(account["email"], "ready", True)
    else:
        db.update_account_status(account["email"], "locked", False)

# 登陆自动操作:账号 密码,并验证是否存在双重认证,若存在自动处理
def auto_login(driver, account, log_message):
    try:
        # 检查是否有iframe
        iframes = driver.find_elements(By.TAG_NAME, "iframe")
        if iframes:
            WebDriverWait(driver, 10).until(
                EC.frame_to_be_available_and_switch_to_it((By.TAG_NAME, "iframe"))
            )
            log_message("✅ 已切换到iframe")
        else:
            log_message("ℹ️ 未检测到iframe，直接在主页面操作")

        # 邮箱输入
        email_input = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.ID, "account_name_text_field"))
        )
        email_input.clear()
        email_input.send_keys(account["email"])
        log_message("✅ 已输入邮箱")
        continue_button = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.ID, "sign-in"))
        )
        driver.execute_script("arguments[0].click();", continue_button)
        log_message("✅ 已点击继续按钮")
        try:
            password_input = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.ID, "password_text_field"))
            )
            driver.execute_script("arguments[0].scrollIntoView(true);", password_input)
            password_input.clear()
            password_input.send_keys(account["password"])
            log_message("✅ 已输入密码")
            sign_in_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.ID, "sign-in"))
            )
            driver.execute_script("arguments[0].scrollIntoView(true);", sign_in_button)
            driver.execute_script("arguments[0].click();", sign_in_button)
            log_message("✅ 已点击登录按钮")

            # 超智能双重认证检测：使用WebDriverWait主动等待元素出现
            old_url = driver.current_url
            two_fa_handled = False

            log_message("🔍 智能检测双重认证界面或页面跳转...")

            try:
                # 使用WebDriverWait同时等待多个条件：双重认证输入框出现 OR URL变化
                def check_2fa_or_url_change(driver):
                    # 检查URL是否变化
                    if driver.current_url != old_url:
                        return "url_changed"

                    # 检查双重认证输入框（在iframe中或主页面中）
                    try:
                        # 先检查主页面
                        code_inputs = driver.find_elements(By.CSS_SELECTOR, "input.form-security-code-input")
                        if len(code_inputs) >= 6:
                            # 验证页面文本包含双重认证关键词
                            page_text = driver.page_source
                            keywords = ["双重认证", "验证码", "安全验证"]
                            if any(k in page_text for k in keywords):
                                return "2fa_detected_main"

                        # 检查iframe中的双重认证
                        iframes = driver.find_elements(By.TAG_NAME, "iframe")
                        if iframes:
                            current_frame = None
                            try:
                                # 保存当前frame状态
                                try:
                                    current_frame = driver.execute_script("return window.frameElement")
                                except:
                                    current_frame = None

                                driver.switch_to.frame(iframes[0])
                                code_inputs = driver.find_elements(By.CSS_SELECTOR, "input.form-security-code-input")
                                if len(code_inputs) >= 6:
                                    page_text = driver.page_source
                                    keywords = ["双重认证", "验证码", "安全验证"]
                                    if any(k in page_text for k in keywords):
                                        return "2fa_detected_iframe"

                            except Exception:
                                pass
                            finally:
                                # 恢复到原来的frame
                                try:
                                    if current_frame is None:
                                        driver.switch_to.default_content()
                                    else:
                                        driver.switch_to.frame(current_frame)
                                except:
                                    driver.switch_to.default_content()
                    except Exception:
                        pass

                    return False

                # 智能等待：最多等待10秒，每0.5秒检查一次
                result = WebDriverWait(driver, 10).until(check_2fa_or_url_change)

                if result == "url_changed":
                    log_message("✅ 智能检测：登录后URL已变化，无需双重认证")
                elif result in ["2fa_detected_main", "2fa_detected_iframe"]:
                    location = "主页面" if result == "2fa_detected_main" else "iframe"
                    log_message(f"✅ 智能检测：在{location}中发现双重认证界面，立即处理...")

                    # 立即处理双重认证
                    two_fa_result = handle_two_factor_auth(driver, account, log_message)
                    if not two_fa_result:
                        log_message("❌ 双重认证处理失败")
                        return False
                    handle_trust_browser(driver, log_message)
                    two_fa_handled = True

            except Exception as e:
                log_message(f"⚠️ 智能检测超时或出错，使用备用检测方案: {str(e)}")
                # 备用方案：直接尝试处理双重认证
                two_fa_result = handle_two_factor_auth(driver, account, log_message)
                if two_fa_result:
                    handle_trust_browser(driver, log_message)
                    two_fa_handled = True
                else:
                    log_message("⚠️ 备用检测也未发现双重认证，可能登录成功或存在其他问题")

            # 如果处理了双重认证，等待页面跳转
            if two_fa_handled:
                max_retries = 5
                for attempt in range(max_retries):
                    if driver.current_url != old_url:
                        log_message(f"✅ 双重认证处理后URL已变化 (第{attempt+1}次检测)")
                        break
                    else:
                        if attempt < max_retries - 1:
                            log_message(f"⏳ 等待双重认证后页面跳转... (第{attempt+1}次重试)")
                            time.sleep(2)
                        else:
                            log_message(f"❌ 双重认证处理后未检测到页面跳转")
                            driver.save_screenshot("error_after_2fa.png")
                            if iframes:
                                driver.switch_to.default_content()
                            return False

        except Exception as e:
            log_message(f"❌ 密码输入或登录按钮点击失败：{str(e)}")
            driver.save_screenshot("error_password_input.png")
            if iframes:
                driver.switch_to.default_content()
            return False
        if iframes:
            driver.switch_to.default_content()
            log_message("✅ 已切回主frame")
        return True
    except Exception as e:
        log_message(f"❌ 普通登录页面自动填充失败: {repr(e)}")
        driver.save_screenshot("error_auto_login.png")
        try:
            driver.switch_to.default_content()
        except:
            pass
        return False

# 隐私弹窗处理    
def handle_privacy_popup(driver, log_message):
    """处理隐私弹窗"""
    try:
        log_message("🔄 检查并处理隐私弹窗...")
        wait_for_page_load(driver, 10)
        # 智能等待隐私弹窗出现，而不是固定等待3秒
        try:
            WebDriverWait(driver, 5).until(
                lambda d: d.find_element(By.CSS_SELECTOR, "div.rc-overlay.rs-signin-consent-overlay") or
                         "Apple 和你的数据隐私" in d.page_source
            )
        except:
            # 如果没有检测到隐私弹窗，使用最小等待时间
            time.sleep(0.5)
        privacy_detected = False
        try:
            privacy_overlay = driver.find_element(By.CSS_SELECTOR, "div.rc-overlay.rs-signin-consent-overlay")
            if privacy_overlay:
                log_message("ℹ️ 通过CSS选择器检测到隐私弹窗")
                privacy_detected = True
        except:
            log_message("ℹ️ CSS选择器未检测到隐私弹窗")
        if not privacy_detected:
            try:
                privacy_title = driver.find_element(By.XPATH, "//h2[contains(text(), 'Apple') and contains(text(), '隐私')]")
                if privacy_title:
                    log_message("ℹ️ 通过标题文本检测到隐私弹窗")
                    privacy_detected = True
            except:
                log_message("ℹ️ 标题文本未检测到隐私弹窗")
        if not privacy_detected:
            try:
                iframes = driver.find_elements(By.TAG_NAME, "iframe")
                for iframe in iframes:
                    try:
                        driver.switch_to.frame(iframe)
                        privacy_title = driver.find_elements(By.XPATH, "//h2[contains(text(), 'Apple') and contains(text(), '隐私')]")
                        if privacy_title:
                            log_message("ℹ️ 在iframe中检测到隐私弹窗")
                            privacy_detected = True
                            break
                        driver.switch_to.default_content()
                    except:
                        driver.switch_to.default_content()
                        continue
            except:
                log_message("ℹ️ iframe检测未发现隐私弹窗")
        if not privacy_detected:
            try:
                page_source = driver.page_source
                if "rs-signin-consent-overlay" in page_source and "Apple 和你的数据隐私" in page_source:
                    log_message("ℹ️ 在页面源码中检测到隐私弹窗")
                    privacy_detected = True
            except:
                log_message("ℹ️ 页面源码检测失败")
        if privacy_detected:
            log_message("ℹ️ 检测到隐私弹窗，开始处理...")
            try:
                WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "div.rc-overlay.rs-signin-consent-overlay"))
                )
                WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "div.r-fade-transition-enter-done"))
                )
                WebDriverWait(driver, 10).until(
                    EC.visibility_of_element_located((By.CSS_SELECTOR, "h2.rs-signin-consent-overlay-header"))
                )
                privacy_policy_checkbox = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.ID, "signIn-guestLogin-consentOverlay-policiesAccepted"))
                )
                driver.execute_script("arguments[0].click();", privacy_policy_checkbox)
                data_handling_checkbox = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.ID, "signIn-guestLogin-consentOverlay-dataOutSideMyCountry"))
                )
                driver.execute_script("arguments[0].click();", data_handling_checkbox)
                agree_and_continue_button_privacy = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, "//button[text()='同意并继续']"))
                )
                driver.execute_script("arguments[0].click();", agree_and_continue_button_privacy)
                WebDriverWait(driver, 10).until_not(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "div.rc-overlay.rs-signin-consent-overlay"))
                )
                log_message("✅ 成功处理隐私弹窗")
            except Exception as e:
                log_message(f"ℹ️ 未找到隐私弹窗内容或处理失败：{str(e)}")
                try:
                    overlay = driver.find_element(By.CSS_SELECTOR, "div[data-core-overlay-cover]")
                    driver.execute_script("arguments[0].style.display = 'none';", overlay)
                except:
                    pass
        else:
            log_message("ℹ️ 无需处理隐私弹窗，继续执行后续步骤")
    except Exception as e:
        log_message(f"ℹ️ 处理隐私弹窗时发生错误：{str(e)}")
    finally:
        try:
            driver.switch_to.default_content()
        except:
            pass

# 双重认证处理
def handle_two_factor_auth(driver, account, log_message):
    """处理双重认证"""
    try:
        log_message("🔄 检查是否需要双重认证...")
        page_text = driver.page_source
        keywords = ["双重认证", "验证码", "安全验证"]
        has_keyword = any(k in page_text for k in keywords)
        code_inputs = []
        iframes = driver.find_elements(By.TAG_NAME, "iframe")
        if iframes:
            try:
                WebDriverWait(driver, 10).until(
                    EC.frame_to_be_available_and_switch_to_it((By.TAG_NAME, "iframe"))
                )
                code_inputs = driver.find_elements(By.CSS_SELECTOR, "input.form-security-code-input")
            except Exception as e:
                log_message(f"❌ 切换iframe或查找输入框失败：{type(e).__name__}: {str(e)}")
                driver.switch_to.default_content()
        else:
            code_inputs = driver.find_elements(By.CSS_SELECTOR, "input.form-security-code-input")
        if has_keyword and len(code_inputs) >= 6:
            log_message(f"✅ 检测到双重认证界面，找到 {len(code_inputs)} 个验证码输入框")
            try:
                verification_code = get_verification_code(account["email"])
                if verification_code and len(verification_code) == 6 and verification_code.isdigit():
                    for i, digit in enumerate(verification_code):
                        code_inputs[i].send_keys(digit)
                        time.sleep(0.1)
                    log_message("✅ 已输入完整验证码")
                    
                    # 等待并点击"信任"按钮
                    try:
                        trust_button = WebDriverWait(driver, 10).until(
                            EC.element_to_be_clickable((By.XPATH, "//button[text()='信任']"))
                        )
                        driver.execute_script("arguments[0].click();", trust_button)
                        log_message("✅ 成功点击'信任'按钮")
                        
                        # 等待页面跳转
                        time.sleep(2)
                        log_message("✅ 双重认证流程完成")
                        return True
                    except Exception as e:
                        log_message(f"⚠️ 未找到'信任'按钮或点击失败：{str(e)}")
                        # 尝试查找其他可能的按钮
                        try:
                            continue_button = WebDriverWait(driver, 5).until(
                                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), '继续') or contains(text(), '确认')]"))
                            )
                            driver.execute_script("arguments[0].click();", continue_button)
                            log_message("✅ 成功点击继续/确认按钮")
                            time.sleep(2)
                            return True
                        except:
                            log_message("⚠️ 未找到其他可点击按钮，但验证码已输入")
                            return True
                else:
                    log_message("❌ 验证码输入无效")
                    return False
            except Exception as e:
                log_message(f"❌ 双重认证处理失败：{type(e).__name__}: {str(e)}")
                return False
            finally:
                driver.switch_to.default_content()
        else:
            log_message("ℹ️ 未检测到双重认证界面，无需处理")
            return True
    except Exception as e:
        log_message(f"ℹ️ 检查双重认证时发生错误：{type(e).__name__}: {str(e)}")
        return False

# 信任浏览器弹窗处理
def handle_trust_browser(driver, log_message):
    """处理信任此浏览器弹窗"""
    try:
        log_message("🔄 检查是否需要信任此浏览器...")
        iframes = driver.find_elements(By.TAG_NAME, "iframe")
        if iframes:
            try:
                WebDriverWait(driver, 10).until(
                    EC.frame_to_be_available_and_switch_to_it((By.TAG_NAME, "iframe"))
                )
                try:
                    trust_button = WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable((By.XPATH, "//button[text()='信任']"))
                    )
                    driver.execute_script("arguments[0].click();", trust_button)
                    log_message("✅ 成功点击'信任'按钮")
                except:
                    log_message("ℹ️ 无需处理'信任此浏览器'弹窗")
            except:
                log_message("ℹ️ 未找到信任弹窗iframe，继续执行下一步...")
            finally:
                driver.switch_to.default_content()
    except Exception as e:
        log_message(f"ℹ️ 处理信任浏览器时发生错误：{str(e)}")

# 智能页面导航
def smart_page_navigation(driver, target_state, button_xpath, button_description, timeout=30):
    """
    智能页面导航：点击按钮并等待页面状态变化
    :param driver: WebDriver实例
    :param target_state: 目标页面状态
    :param button_xpath: 按钮的XPath
    :param button_description: 按钮描述（用于日志）
    :param timeout: 超时时间
    :return: 是否成功导航到目标状态
    """
    try:
        print(f"🔄 开始智能页面导航: {button_description} -> {target_state}")
        
        # 1. 查找并点击按钮
        continue_button = WebDriverWait(driver, 20).until(
            EC.element_to_be_clickable((By.XPATH, button_xpath))
        )
        driver.execute_script("arguments[0].click();", continue_button)
        print(f"✅ 成功点击'{button_description}'按钮")
        
        # 2. 等待页面完全加载
        if not wait_for_page_load(driver, 15):
            print("⚠️ 页面加载等待超时，但继续执行状态检测")
        
        # 3. 等待页面状态变化
        if wait_for_page_state_change(driver, target_state, timeout=timeout, check_interval=2):
            print(f"✅ 成功导航到目标状态: {target_state}")
            return True
        else:
            print(f"❌ 页面状态导航失败，期望: {target_state}")
            driver.save_screenshot(f"error_navigation_to_{target_state}.png")
            return False
            
    except Exception as e:
        print(f"❌ 智能页面导航失败: {str(e)}")
        driver.save_screenshot(f"error_smart_navigation_{target_state}.png")
        return False

# 点击按钮设定,适用于所有按钮点击场景
def click_button_with_xpaths(driver, xpaths, timeout=30, description="按钮"):
    """
    依次尝试多种XPath查找并点击按钮，适用于所有按钮点击场景。
    :param driver: Selenium WebDriver
    :param xpaths: XPath字符串列表
    :param timeout: 等待时间
    :param description: 按钮描述（用于日志）
    :return: True=成功点击，False=全部失败
    """
    for xpath in xpaths:
        try:
            btn = WebDriverWait(driver, timeout).until(
                EC.presence_of_element_located((By.XPATH, xpath))
            )
            driver.execute_script("arguments[0].scrollIntoView(true);", btn)
            driver.execute_script("arguments[0].click();", btn)
            print(f"✅ 成功点击{description}，XPath: {xpath}")
            return True
        except Exception as e:
            print(f"❌ 点击{description}时发生异常，XPath: {xpath}，异常: {e}")
            continue
    print(f"❌ 所有方式均未能点击{description}")
    return False

# 保存错误页面截图和HTML源码
def save_error_artifacts(driver, log_message, prefix="error_navigate_to_payment_details"):
    try:
        # 获取页面完整尺寸
        total_height = driver.execute_script("return document.body.scrollHeight")
        total_width = driver.execute_script("return document.body.scrollWidth")
        viewport_height = driver.execute_script("return window.innerHeight")
        viewport_width = driver.execute_script("return window.innerWidth")
        # 设置浏览器窗口大小为页面完整尺寸
        driver.set_window_size(total_width, total_height)
        time.sleep(1)  # 等待页面重新渲染
        # 截图和保存HTML
        driver.save_screenshot(f"{prefix}.png")
        with open(f"{prefix}.html", "w", encoding="utf-8") as f:
            f.write(driver.page_source)
        # 恢复原始窗口大小
        driver.set_window_size(viewport_width, viewport_height)
    except Exception as e:
        log_message(f"⚠️ 保存错误页面内容时出错: {str(e)}")

# 点击"继续填写地址"导航至送货详情页面
def navigate_to_shipping_details(driver, log_message):
    """导航至送货详情页面"""
    try:
        log_message("🧭 正在尝试进入送货详情页面...")
        xpaths = [
            "//button[@id='rs-checkout-continue-button-bottom']",
            "//button[@data-autom='fulfillment-continue-button']",
            "//button[contains(., '继续填写送货地址')]"
        ]
        if click_button_with_xpaths(driver, xpaths, timeout=30, description="继续填写送货地址按钮"):
            # 这里可以加上等待页面跳转和状态变化的逻辑
            log_message("✅ 成功点击继续填写送货地址按钮，等待页面跳转...")
            # 你可以继续等待页面状态变为shipping_details
            # 例如：wait_for_page_state_change(driver, CheckoutPageState.SHIPPING_DETAILS, timeout=30)
            return True
        else:
            save_error_artifacts()
            log_message("❌ 导航至送货详情页面失败")
            return False
    except Exception as e:
        save_error_artifacts()
        log_message(f"⚠️ 导航至送货详情页面时发生异常: {str(e)}")
        return False

# 点击"继续选择付款方式"导航至付款详情页面
def navigate_to_payment_details(driver, log_message):
    """导航至付款详情页面"""
    try:
        log_message("🧭 正在尝试进入付款详情页面...")
        xpaths = [
            "//button[contains(., '继续选择付款方式')]",
            "//button[@data-autom='payment-continue-button']",
            "//button[@id='rs-checkout-continue-button-payment']"
        ]
        if click_button_with_xpaths(driver, xpaths, timeout=30, description="继续选择付款方式按钮"):
            log_message("✅ 成功点击继续选择付款方式按钮，等待页面跳转...")
            # 关键：点击后等待页面状态变为 payment_details
            if wait_for_page_state_change(driver, CheckoutPageState.PAYMENT_DETAILS, timeout=30):
                log_message("✅ 成功进入付款详情页面")
                return True
            else:
                save_error_artifacts(driver, log_message, prefix="error_navigate_to_payment_details")
                log_message("❌ 页面未跳转到付款详情，流程失败")
                return False
        else:
            save_error_artifacts(driver, log_message, prefix="error_navigate_to_payment_details")
            log_message("❌ 导航至付款详情页面失败")
            return False
    except Exception as e:
        save_error_artifacts(driver, log_message, prefix="error_navigate_to_payment_details")
        log_message(f"⚠️ 导航至付款详情页面时发生异常: {str(e)}")
        return False

# 保存成功页面
def save_final_page_artifacts(driver, account, log_message):
    """保存最终页面的截图和HTML源码"""
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = f"./results/{account['email']}"
        os.makedirs(output_dir, exist_ok=True)

        # 获取页面完整尺寸
        total_height = driver.execute_script("return document.body.scrollHeight")
        total_width = driver.execute_script("return document.body.scrollWidth")
        viewport_height = driver.execute_script("return window.innerHeight")
        viewport_width = driver.execute_script("return window.innerWidth")
        
        # 设置浏览器窗口大小为页面完整尺寸
        driver.set_window_size(total_width, total_height)
        time.sleep(1)  # 等待页面重新渲染
        
        # 截图
        screenshot_filename = f"{output_dir}/final_page_{timestamp}.png"
        driver.save_screenshot(screenshot_filename)
        log_message(f"📸 已保存完整页面截图: {screenshot_filename} (尺寸: {total_width}x{total_height})")

        # HTML源码
        html_filename = f"{output_dir}/final_page_{timestamp}.html"
        with open(html_filename, "w", encoding="utf-8") as f:
            f.write(driver.page_source)
        log_message(f"📄 已保存最终页面HTML: {html_filename}")
        
        # 恢复原始窗口大小
        driver.set_window_size(viewport_width, viewport_height)
        
    except Exception as e:
        log_message(f"⚠️ 保存最终页面内容时出错: {str(e)}")

# 检查错误信息
def check_for_error_message(driver, log_message):
    """检查是否出现错误信息"""
    try:
        error_element = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'error-message')]"))
        )
        log_message(f"❌ 检测到错误信息: {error_element.text}")
        return True
    except TimeoutException:
        log_message("ℹ️ 未检测到错误信息")
        return False

# 提交"立即下单"订单按钮
def click_place_order_button(driver, log_message):
    try:
        # 等待页面完全加载
        WebDriverWait(driver, 10).until(
            lambda d: d.execute_script("return document.readyState") == "complete"
        )
        
        # 添加随机等待，防止反爬虫检测
        time.sleep(random.uniform(1, 2))
        
        # 使用多种定位方式查找按钮，避免stale element reference
        button_found = False
        max_retries = 3
        
        for attempt in range(max_retries):
            try:
                log_message(f"🔄 尝试定位'立即下单'按钮 (第{attempt + 1}次)")
                
                # 方法1：通过ID定位
                try:
                    place_order_button = WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable((By.ID, "rs-checkout-continue-button-bottom"))
                    )
                    button_found = True
                    log_message("✅ 通过ID定位到按钮")
                    break
                except:
                    pass
                
                # 方法2：通过XPath定位
                try:
                    place_order_button = WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), '立即下单')]"))
                    )
                    button_found = True
                    log_message("✅ 通过XPath定位到按钮")
                    break
                except:
                    pass
                
                # 方法3：通过CSS选择器定位
                try:
                    place_order_button = WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, "button[data-autom='continue-button-label']"))
                    )
                    button_found = True
                    log_message("✅ 通过CSS选择器定位到按钮")
                    break
                except:
                    pass
                
                # 方法4：通过class定位
                try:
                    place_order_button = WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, "button.form-button"))
                    )
                    button_found = True
                    log_message("✅ 通过class定位到按钮")
                    break
                except:
                    pass
                
                if not button_found:
                    log_message(f"⚠️ 第{attempt + 1}次尝试未找到按钮，等待后重试...")
                    time.sleep(random.uniform(2, 3))
                    
            except Exception as e:
                log_message(f"⚠️ 第{attempt + 1}次尝试失败: {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(random.uniform(2, 3))
        
        if not button_found:
            log_message("❌ 所有定位方式都失败了")
            driver.save_screenshot("error_button_not_found.png")
            return False
        
        # 滚动到按钮位置
        driver.execute_script("arguments[0].scrollIntoView({block: 'center', behavior: 'smooth'});", place_order_button)
        time.sleep(random.uniform(1, 2))
        
        # 检查按钮是否被禁用
        try:
            if place_order_button.get_attribute("disabled"):
                log_message("❌ 按钮被禁用")
                return False
        except:
            log_message("⚠️ 无法检查按钮状态，继续尝试点击")
        
        # 使用多种点击方式
        click_success = False
        
        # 方式1：JavaScript点击
        try:
            driver.execute_script("arguments[0].click();", place_order_button)
            click_success = True
            log_message("✅ 使用JavaScript点击成功")
        except Exception as e:
            log_message(f"⚠️ JavaScript点击失败: {str(e)}")
        
        # 方式2：ActionChains点击
        if not click_success:
            try:
                ActionChains(driver).move_to_element(place_order_button).pause(random.uniform(0.3, 0.8)).click().perform()
                click_success = True
                log_message("✅ 使用ActionChains点击成功")
            except Exception as e:
                log_message(f"⚠️ ActionChains点击失败: {str(e)}")
        
        # 方式3：直接点击
        if not click_success:
            try:
                place_order_button.click()
                click_success = True
                log_message("✅ 使用直接点击成功")
            except Exception as e:
                log_message(f"⚠️ 直接点击失败: {str(e)}")
        
        if click_success:
            log_message("✅ 已点击'立即下单'按钮")
            return True
        else:
            log_message("❌ 所有点击方式都失败了")
            driver.save_screenshot("error_click_failed.png")
            return False
            
    except Exception as e:
        log_message(f"❌ 点击'立即下单'失败：{str(e)}")
        driver.save_screenshot("error_place_order_button.png")
        return False

# 验证订单成功
def verify_redirect_to_credit_page(driver, log_message, email):
    try:
        # 等待页面标题或内容出现"订单确认"或"谢谢"
        WebDriverWait(driver, 30).until(
            lambda d: (
                "谢谢：订单确认" in d.title or 
                "订单确认" in d.title or
                d.find_elements(By.XPATH, "//*[contains(text(), '订单确认')]") or
                d.find_elements(By.XPATH, "//*[contains(text(), '谢谢')]") or
                "你的订单正在等待付款" in d.page_source or
                re.search(r'W\d{10}', d.page_source) or
                d.find_elements(By.XPATH, "//button[contains(text(), '现在支付')]") or
                d.find_elements(By.XPATH, "//a[contains(text(), '现在支付')]")
            )
        )
        log_message("✅ 已成功跳转到订单确认页面")
        
        # 静默提取订单号（不显示日志）
        order_number = extract_order_number_from_page(driver)
        
        # 构建订单URL
        if order_number:
            order_url = f"https://www.apple.com.cn/xc/cn/vieworder/{order_number}/{email}"
        else:
            # 如果提取失败，使用通用订单页面
            order_url = "https://www.apple.com.cn/shop/order"
        
        # 尝试打开Chrome浏览器（只尝试一次）
        try:
            chrome_path = "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
            log_message(f"🔍 尝试启动Chrome: {chrome_path}")
            subprocess.Popen([chrome_path, order_url])
            log_message(f"✅ 已启动Chrome浏览器打开订单页面")
        except Exception as e:
            log_message(f"⚠️ 启动Chrome失败: {e}")
            log_message(f"🔄 尝试使用默认浏览器打开: {order_url}")
            webbrowser.open(order_url)
            log_message(f"✅ 已使用默认浏览器打开订单页面")
            
        return True
    except TimeoutException:
        log_message("❌ 未在30秒内跳转到订单确认页面")
        driver.save_screenshot("error_order_confirmation_timeout.png")
        return False
    except Exception as e:
        log_message(f"❌ 验证订单确认页面时出错: {str(e)}")
        driver.save_screenshot("error_order_confirmation.png")
        return False     

# 提取订单号码
def extract_order_number_from_page(driver):
    """静默提取订单号，不输出日志"""
    try:
        page_source = driver.page_source
        import re
        
        # 查找W开头+10位数字的订单号
        order_match = re.search(r'"metricsReportWebOrderNumberString":"(W\d{10})"', page_source)
        if order_match:
            return order_match.group(1)
        
        # 备用方法
        order_match = re.search(r'(W\d{10})', page_source)
        if order_match:
            return order_match.group(1)
            
        return None
    except:
        return None

# 24分期+建设银行 ccb
def handle_installment_payment_ccb(driver, log_message):
    """
    仅负责选择中国建设银行 + 24期分期付款方式。
    不处理后续流程控制（如点击"检查订单"、"立即下单"等）。

    参数:
        driver: WebDriver 实例
        log_message: 日志记录函数

    返回:
        True: 成功选择了银行和分期方式
        False: 操作失败
    """
    try:
        log_message("🔄 开始处理分期付款选项...")

        # 智能等待付款方式区域完全加载（包括银行选项）
        def payment_options_loaded(driver):
            try:
                # 检查付款方式区域是否存在
                payment_area = driver.find_element(By.CSS_SELECTOR, "div.rs-payment-options")
                # 检查银行选项是否已加载
                bank_options = driver.find_elements(By.XPATH, "//label[./span/span/img[contains(@alt, '银行')]]")
                return payment_area and len(bank_options) > 0
            except:
                return False

        WebDriverWait(driver, 30).until(payment_options_loaded)
        log_message("✅ 付款方式区域已加载")

        # 智能随机等待，防止反爬虫检测
        time.sleep(random.uniform(0.8, 1.5))

        # 第一步：选择中国建设银行分期付款
        log_message("🔄 尝试选择中国建设银行分期付款...")
        ccb_installments_label = WebDriverWait(driver, 20).until(
            EC.element_to_be_clickable((By.XPATH, "//label[./span/span/img[@alt='中国建设银行']]"))
        )
        driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", ccb_installments_label)
        time.sleep(random.uniform(0.5, 1.5))
        ActionChains(driver).move_to_element(ccb_installments_label).pause(random.uniform(0.3, 0.8)).perform()
        time.sleep(random.uniform(0.5, 1.0))
        driver.execute_script("arguments[0].click();", ccb_installments_label)
        log_message("✅ 成功选择中国建设银行分期付款")
        time.sleep(random.uniform(1, 2))

        # 第二步：在中国建设银行分期区域下查找24期分期
        log_message("🔄 尝试在中国建设银行分期区域查找24期分期...")

        try:
            # 先尝试原有方式
            wait_for_element_present(driver, (By.XPATH, "//input[@value='24']"), 15)

            ccb_input = WebDriverWait(driver, 25).until(
                EC.presence_of_element_located((By.XPATH,
                                                  "//label[./span/span/img[@alt='中国建设银行']]/preceding-sibling::input[@type='radio']"))
            )
            ccb_input_id = ccb_input.get_attribute("id")
            base_id = ccb_input_id.replace('checkout.billing.billingoptions.', '')
            data_autom_24_period = f"{base_id}-24"
            installment_24_input = WebDriverWait(driver, 20).until(
                EC.presence_of_element_located((By.XPATH,
                                                  f"//input[@data-autom='{data_autom_24_period}' and @value='24']"))
            )
            driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", installment_24_input)
            time.sleep(random.uniform(0.5, 1.5))
            ActionChains(driver).move_to_element(installment_24_input).pause(random.uniform(0.3, 0.8)).perform()
            time.sleep(random.uniform(0.5, 1.0))
            driver.execute_script("arguments[0].click();", installment_24_input)
            log_message("✅ 成功选择24期分期（原有方式）")

        except Exception as e:
            log_message(f"⚠️ 原有方式查找24期分期失败：{str(e)}，尝试调试方式")
            try:
                # 调试方式定位到中国建设银行分期区域
                ccb_div = driver.find_element(By.XPATH,
                                             "//div[@id[contains(.,'installments') and contains(.,'selector')] and .//img[@alt='中国建设银行']]")
                labels = ccb_div.find_elements(By.XPATH, ".//label")
                log_message(f"📝 [调试] 区域内所有label:")
                for label in labels:
                    label_for = label.get_attribute('for')
                    label_text = label.text
                    log_message(f"label for={label_for}, text={label_text}")

                inputs = ccb_div.find_elements(By.XPATH, ".//input")
                log_message(f"📝 [调试] 区域内所有input:")
                for inp in inputs:
                    inp_id = inp.get_attribute('id')
                    inp_value = inp.get_attribute('value')
                    inp_type = inp.get_attribute('type')
                    log_message(f"input id={inp_id}, value={inp_value}, type={inp_type}")

                label_24 = ccb_div.find_element(By.XPATH,
                                                 ".//span[@class='form-selector-title' and contains(text(), '24 期')]/ancestor::label")
                input_id = label_24.get_attribute('for')
                if input_id:
                    installment_24_input = ccb_div.find_element(By.ID, input_id)
                    if installment_24_input.get_attribute('value') == '24':
                        driver.execute_script("arguments[0].scrollIntoView({block: 'center'});",
                                              installment_24_input)
                        time.sleep(random.uniform(0.5, 1.5))
                        ActionChains(driver).move_to_element(installment_24_input).pause(
                            random.uniform(0.3, 0.8)).perform()
                        time.sleep(random.uniform(0.5, 1.0))
                        driver.execute_script("arguments[0].click();", installment_24_input)
                        log_message("✅ 成功选择24期分期（区域内）")
                    else:
                        raise Exception('24期input的value不是24')
                else:
                    raise Exception('24期label未找到for属性')

            except Exception as e2:
                log_message(f"⚠️ 调试方式查找24期分期也失败：{str(e2)}")
                return False

        time.sleep(random.uniform(1, 2))
        log_message("🎉 分期付款设置完成（银行 + 分期数）")
        return True

    except Exception as e:
        log_message(f"❌ 处理分期付款时发生错误：{str(e)}")
        driver.save_screenshot("error_installment_payment.png")

        # 尝试备用方案
        try:
            log_message("🔄 尝试备用方案1：直接通过文本查找中国建设银行")
            ccb_installments_label = WebDriverWait(driver, 20).until(
                EC.element_to_be_clickable((By.XPATH, "//label[contains(., '中国建设银行')]"))
            )
            driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", ccb_installments_label)
            time.sleep(random.uniform(0.5, 1.5))
            driver.execute_script("arguments[0].click();", ccb_installments_label)
            log_message("✅ 通过备用方案1成功选择中国建设银行分期付款")

            time.sleep(random.uniform(1, 2))

            log_message("🔄 尝试备用方案2：直接查找24期选项")
            installment_24_input = WebDriverWait(driver, 20).until(
                EC.presence_of_element_located((By.XPATH, "//input[@value='24' and contains(@id, 'installment')]"))
            )
            driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", installment_24_input)
            time.sleep(random.uniform(0.5, 1.5))
            driver.execute_script("arguments[0].click();", installment_24_input)
            log_message("✅ 通过备用方案2成功选择24期分期")

            log_message("✅ 通过备用方案完成了分期付款设置")
            return True

        except Exception as e2:
            log_message(f"❌ 备用方案也失败：{str(e2)}")
            return False

# 24分期+招商银行 cmb
def handle_installment_payment_cmb(driver, log_message):
    """
    处理招商银行分期付款（选择招商银行+24期）
    """
    try:
        log_message("🔄 开始处理招商银行分期付款选项...")

        # 等待付款方式区域加载完成
        WebDriverWait(driver, 30).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "div.rs-payment-options"))
        )
        log_message("✅ 付款方式区域已加载")
        time.sleep(random.uniform(1, 2))

        # 第一步：选择招商银行分期付款
        log_message("🔄 尝试选择招商银行分期付款...")
        cmb_installments_label = WebDriverWait(driver, 20).until(
            EC.element_to_be_clickable((By.XPATH, "//label[./span/span/img[@alt='招商银行']]"))
        )
        driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", cmb_installments_label)
        time.sleep(random.uniform(0.5, 1.5))
        ActionChains(driver).move_to_element(cmb_installments_label).pause(random.uniform(0.3, 0.8)).perform()
        time.sleep(random.uniform(0.5, 1.0))
        driver.execute_script("arguments[0].click();", cmb_installments_label)
        log_message("✅ 成功选择招商银行分期付款")
        time.sleep(random.uniform(1, 2))

        # 第二步：在招商银行分期区域下查找24期分期
        log_message("🔄 尝试在招商银行分期区域查找24期分期...")
        try:
            wait_for_element_present(driver, (By.XPATH, "//input[@value='24']"), 15)
            cmb_input = WebDriverWait(driver, 25).until(
                EC.presence_of_element_located((By.XPATH,
                    "//label[./span/span/img[@alt='招商银行']]/preceding-sibling::input[@type='radio']"))
            )
            cmb_input_id = cmb_input.get_attribute("id")
            base_id = cmb_input_id.replace('checkout.billing.billingoptions.', '')
            data_autom_24_period = f"{base_id}-24"
            installment_24_input = WebDriverWait(driver, 20).until(
                EC.presence_of_element_located((By.XPATH,
                    f"//input[@data-autom='{data_autom_24_period}' and @value='24']"))
            )
            driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", installment_24_input)
            time.sleep(random.uniform(0.5, 1.5))
            ActionChains(driver).move_to_element(installment_24_input).pause(random.uniform(0.3, 0.8)).perform()
            time.sleep(random.uniform(0.5, 1.0))
            driver.execute_script("arguments[0].click();", installment_24_input)
            log_message("✅ 成功选择24期分期（招商银行）")
        except Exception as e:
            log_message(f"⚠️ 查找24期分期失败：{str(e)}，尝试备用方案")
            try:
                cmb_div = driver.find_element(By.XPATH,
                    "//div[@id[contains(.,'installments') and contains(.,'selector')] and .//img[@alt='招商银行']]")
                label_24 = cmb_div.find_element(By.XPATH,
                    ".//span[@class='form-selector-title' and contains(text(), '24 期')]/ancestor::label")
                input_id = label_24.get_attribute('for')
                if input_id:
                    installment_24_input = cmb_div.find_element(By.ID, input_id)
                    if installment_24_input.get_attribute('value') == '24':
                        driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", installment_24_input)
                        time.sleep(random.uniform(0.5, 1.5))
                        ActionChains(driver).move_to_element(installment_24_input).pause(random.uniform(0.3, 0.8)).perform()
                        time.sleep(random.uniform(0.5, 1.0))
                        driver.execute_script("arguments[0].click();", installment_24_input)
                        log_message("✅ 成功选择24期分期（区域内-招商银行）")
                    else:
                        raise Exception('24期input的value不是24')
                else:
                    raise Exception('24期label未找到for属性')
            except Exception as e2:
                log_message(f"❌ 备用方案也失败：{str(e2)}")
                return False
        time.sleep(random.uniform(1, 2))
        log_message("🎉 招商银行分期付款设置完成（银行 + 分期数）")
        return True
    except Exception as e:
        log_message(f"❌ 处理招商银行分期付款时发生错误：{str(e)}")
        driver.save_screenshot("error_installment_payment_cmb.png")
        return False

# 抢购的主要流程,包含其他重要函数,页面检测-导航对应操作-点击对应按钮-选择不同支付方式-待提交(接近完成): test_mode = TRUE 测试模式
def fill_shipping_and_payment_flow(driver, account, log_message, test_mode=True, stop_flag=None):
    """
    主流程：填写送货地址、选择付款方式、处理分期付款等
    
    参数:
        driver: Selenium WebDriver 实例
        account: 账户信息字典，包含 email 等字段
        log_message: 日志记录函数
        test_mode: 是否启用测试模式（不提交真实订单）
        stop_flag: 停止标志，用于中断流程
    
    返回:
        bool: 是否成功完成流程
    """
    def check_stop_flag():
        """检查停止标志"""
        if stop_flag and stop_flag.is_set():
            log_message("🛑 收到停止信号，正在退出自动化流程...")
            return True
        return False
    
    current_state = detect_page_state(driver, max_retries=3, retry_delay=2)
    log_message(f"🔄 当前页面状态: {current_state}")

    # 检查停止标志
    if check_stop_flag():
        return False

    # 如果当前处于订单选项页，执行智能导航流程
    if current_state == CheckoutPageState.ORDER_OPTIONS:
        log_message("🔄 检测到订单选项页面，开始智能导航流程...")
        if not navigate_to_shipping_details(driver, log_message):
            return False
        if not navigate_to_payment_details(driver, log_message):
            return False
    else:
        # 否则尝试点击通用"继续"按钮
        try:
            log_message("🔄 尝试点击通用'继续'按钮")
            continue_button = WebDriverWait(driver, 20).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(., '继续') or contains(., '继续填写送货地址') or contains(., '继续选择付款方式')]"))
            )
            driver.execute_script("arguments[0].click();", continue_button)
            log_message("✅ 成功点击'继续'按钮")
            wait_for_page_load(driver, 15)
        except Exception as e:
            log_message(f"❌ 点击'继续'按钮失败：{str(e)}")
            driver.save_screenshot("error_continue_button.png")
            return False

    # 检查停止标志
    if check_stop_flag():
        return False

    # 确保已经进入付款详情页面
    current_state = detect_page_state(driver, max_retries=3, retry_delay=2)
    log_message(f"🔄 处理分期付款前页面状态: {current_state}")
    if current_state != CheckoutPageState.PAYMENT_DETAILS:
        log_message(f"❌ 当前页面状态为 {current_state}，不是付款详情页面")
        driver.save_screenshot(f"error_wrong_page_state_{current_state}.png")
        return False

    # 检查停止标志
    if check_stop_flag():
        return False

    # 处理分期付款（根据账号选择银行+分期方式）
    installment_type = account.get("installment", "ccb")
    if installment_type == "ccb":
        if not handle_installment_payment_ccb(driver, log_message):
            log_message("❌ 建设银行分期付款设置失败")
            return False
    elif installment_type == "cmb":
        if not handle_installment_payment_cmb(driver, log_message):
            log_message("❌ 招商银行分期付款设置失败")
            return False
    else:
        log_message(f"❌ 未知分期类型: {installment_type}")
        return False

    # 检查停止标志
    if check_stop_flag():
        return False

    # 分期付款设置完成后，模拟人类思考/犹豫，随机等待0.2~0.5秒
    log_message("⏳ 分期付款设置完成，等待片刻后点击'检查订单'按钮...")
    time.sleep(random.uniform(0.2, 0.5))

    # 点击"检查订单"按钮的独立函数
    def click_review_order_button(driver, log_message):
        """
        点击"检查订单"按钮并等待进入最终确认页面
        """
        try:
            # 等待按钮完全加载
            WebDriverWait(driver, 20).until(
                EC.presence_of_element_located((By.ID, "rs-checkout-continue-button-bottom"))
            )
            # 然后等待可点击
            review_button = WebDriverWait(driver, 20).until(
                EC.element_to_be_clickable((By.ID, "rs-checkout-continue-button-bottom"))
            )
            # 滚动到按钮位置
            driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", review_button)
            time.sleep(random.uniform(0.5, 1.5))
            # 鼠标悬停
            ActionChains(driver).move_to_element(review_button).pause(random.uniform(0.3, 0.8)).perform()
            # 随机延迟
            time.sleep(random.uniform(0.5, 1.0))
            driver.execute_script("arguments[0].click();", review_button)
            log_message("✅ 已点击'检查订单'按钮")
            # 使用智能等待页面状态变化
            if wait_for_page_state_change(driver, CheckoutPageState.REVIEW_ORDER, timeout=20, check_interval=1):
                log_message("✅ 成功进入最终确认页面")
                return True
            else:
                log_message(f"❌ 页面状态变化超时，未进入最终确认页面")
                driver.save_screenshot("error_not_review.png")
                return False
        except Exception as e:
            log_message(f"❌ 点击'检查订单'失败：{str(e)}")
            driver.save_screenshot("error_review_click.png")
            return False

    # 点击"检查订单"按钮
    if not click_review_order_button(driver, log_message):
        return False
    
    # 检查停止标志
    if check_stop_flag():
        return False
    
    # 测试模式下停止流程
    if test_mode:
        log_message("🎉 测试模式：成功进入最终确认页面，流程终止。")
        save_final_page_artifacts(driver, account, log_message)
        # 播放成功音效
        sound_manager.play_success_sound()
        return "测试完成"

    # 检查停止标志
    if check_stop_flag():
        return False

    # 非测试模式，继续点击"立即下单"按钮
    if not click_place_order_button(driver, log_message):
        return False

    # 检查停止标志
    if check_stop_flag():
        return False

    # 验证是否跳转到下一步（订单正式提交成功页面）
    if not verify_redirect_to_credit_page(driver, log_message, account["email"]):
        return False

    # 检查停止标志
    if check_stop_flag():
        return False

    # 保存最终页面信息
    save_final_page_artifacts(driver, account, log_message)

    # 提取订单号
    order_number = extract_order_number_from_page(driver)
    if order_number:
        log_message(f"✅ 成功完成整个结账流程，订单已提交，订单号: {order_number}")
        return order_number
    else:
        log_message("✅ 成功完成整个结账流程，订单已提交，但未能提取到订单号")
        return "订单提交成功"

# 账号日志设定
def setup_logger(account_email):
    """为每个账号创建独立的日志记录器"""
    if not os.path.exists('logs'):
        os.makedirs('logs', exist_ok=True)
    
    log_filename = f'logs/iphone_bot_{account_email}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
    
    logger = logging.getLogger(account_email)
    logger.setLevel(logging.INFO)
    
    # 文件处理器
    file_handler = logging.FileHandler(log_filename, encoding='utf-8')
    file_handler.setFormatter(logging.Formatter('%(asctime)s [账号: %(account)s] [步骤 %(step)d] %(message)s'))
    logger.addHandler(file_handler)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter('%(asctime)s [账号: %(account)s] [步骤 %(step)d] %(message)s'))
    logger.addHandler(console_handler)
    
    return logger

# 账号启动设定并开始进行添加购物车: test_mode = TRUE 测试模式
def run_automation_for_account(account, log_callback=None, two_fa_input_callback=None, use_proxy=False, target_url=None, test_mode=True, stop_flag=None):
    """为单个账号运行自动化流程"""
    logger = setup_logger(account["email"])
    step_counter = 0
    driver = None  # 初始化driver变量

    # 性能监控：记录开始时间
    account_start_time = time.time()
    account_email = account["email"]

    def log_message(message):
        nonlocal step_counter
        step_counter += 1
        logger.info(message, extra={'step': step_counter, 'account': account["email"]})
        if log_callback:
            log_callback(f"[{account['email']}] {message}")
        # 添加到全局日志队列
        add_log(account["email"], message)

    def check_stop_flag():
        """检查停止标志"""
        if stop_flag and stop_flag.is_set():
            log_message("🛑 收到停止信号，正在退出自动化流程...")
            return True
        return False

    try:
        log_message("🔄 开始执行自动化流程...")

        # 检查停止标志
        if check_stop_flag():
            return False

        # 链接监控已经在run_parallel_automation中完成，这里直接使用账号 ACCOUNTS 提供的抢购链接
        if target_url:
            log_message(f"🎯 使用抢购链接: {target_url}")
        else:
            # 使用默认的iPhone购买链接进行抢购,不同型号需要进行修改
            target_url = "https://www.apple.com.cn/shop/buy-iphone/iphone-16-pro/MYTN3CH/A"
            log_message(f"ℹ️ 使用默认链接: {target_url}")

        # 检查停止标志
        if check_stop_flag():
            return False

        # 代理设置
        chrome_options = Options()
        selected_proxy = None
        proxy_host = None
        proxy_port = None
        proxy_username = None
        proxy_password = None
        
        if use_proxy:
            # 从代理池获取代理
            account_email = account["email"]
            selected_proxy = proxy_pool.get_proxy(account_email)
            
            if selected_proxy:
                proxy_host = selected_proxy['host']
                proxy_port = selected_proxy['port']
                proxy_username = selected_proxy.get('username')
                proxy_password = selected_proxy.get('password')
                
                # 添加详细的调试信息
                log_message(f"🔍 代理详情 - 主机: {proxy_host}, 端口: {proxy_port}")
                log_message(f"🔍 代理详情 - 用户名: {proxy_username}, 密码: {proxy_password}")
                
                # 设置代理服务器但不包含认证信息
                proxy_url = f"http://{proxy_host}:{proxy_port}"
                chrome_options.add_argument(f'--proxy-server={proxy_url}')
                log_message(f"✅ 已为账号 {account_email} 分配代理: {proxy_host}:{proxy_port}")
                
                # 设置环境变量代理（可选，双保险）
                if proxy_username and proxy_password:
                    auth_proxy_url = f"http://{proxy_username}:{proxy_password}@{proxy_host}:{proxy_port}"
                    os.environ['HTTP_PROXY'] = auth_proxy_url
                    os.environ['HTTPS_PROXY'] = auth_proxy_url
                else:
                    os.environ['HTTP_PROXY'] = proxy_url
                    os.environ['HTTPS_PROXY'] = proxy_url
            else:
                log_message("⚠️ 代理池中无可用代理，将使用直连模式")
        else:
            log_message("ℹ️ 已禁用代理，将使用直连模式")

        # Chrome cookies设定: 为每个账号创建独立的 Chrome 用户数据目录（浏览器配置/缓存/登录状态等）
        user_data_dir = os.path.join(os.getcwd(), f"chrome_profile_{account['email']}")
        if not os.path.exists(user_data_dir):
            os.makedirs(user_data_dir)
        chrome_options.add_argument(f"--user-data-dir={user_data_dir}")

        # 隐身、稳定、反检测设定
        chrome_options.add_argument("--headless=new")   # 新版无头模式: 更加稳定、兼容性更好
        chrome_options.add_argument("--disable-gpu")    # 禁用GPU硬件加速: 在无头模式下通常推荐加上这条
        chrome_options.add_argument("--no-sandbox")     #禁用Chrome的沙盒安全机制
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")    # 隐藏Selenium自动化特征
        # 伪装Mac Chrome浏览器
        chrome_options.add_argument("user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")

        # 检查停止标志
        if check_stop_flag():
            return False

        # 初始化 WebDriver
        service = Service("/usr/local/bin/chromedriver")
        driver = webdriver.Chrome(service=service, options=chrome_options)

        # 如果使用代理且有认证信息，则设置代理认证
        if use_proxy and selected_proxy and proxy_username and proxy_password:
            import base64
            try:
                # 先启用 Network
                driver.execute_cdp_cmd('Network.enable', {})
                
                # 设置代理认证头
                auth_string = f"{proxy_username}:{proxy_password}"
                # 使用utf-8编码确保特殊字符正确处理
                encoded_auth = base64.b64encode(auth_string.encode('utf-8')).decode('utf-8')
                driver.execute_cdp_cmd('Network.setExtraHTTPHeaders', {
                    'headers': {
                        'Proxy-Authorization': f'Basic {encoded_auth}'
                    }
                })
                log_message(f"✅ 已设置代理认证信息 (用户名: {proxy_username}, 编码后: {encoded_auth[:20]}...)")
            except Exception as e:
                log_message(f"⚠️ 设置代理认证失败: {str(e)}")

        # 检查代理出口IP(可以不启用)
        #try:
        #    driver.get("http://httpbin.org/ip")
        #    ip_info = driver.find_element(By.TAG_NAME, "body").text
        #    log_message(f"🌐 当前浏览器出口IP: {ip_info}")
        #except Exception as e:
        #    log_message(f"⚠️ 检查出口IP失败: {e}")

        # 隐藏自动化特征（关键一步）
        driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
            "source": """
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined
                })
            """
        })

        # 检查停止标志
        if check_stop_flag():
            return False

        # 选择账号专属URL，否则用target_url，否则用默认
        use_url = account.get("url") or target_url or "https://www.apple.com.cn/shop/buy-iphone/iphone-16-pro/MYTN3CH/A"
        driver.get(use_url)
        log_message(f"当前页面标题: {driver.title}")
        log_message(f"当前页面URL: {driver.current_url}")

        # 等待页面加载完成
        wait_for_page_load(driver, 10)

        # 检查停止标志
        if check_stop_flag():
            return False

        # 点击"无折抵换购"
        try:
            no_trade_in_input = WebDriverWait(driver, 20).until(
                EC.presence_of_element_located((By.ID, "noTradeIn"))
            )
            driver.execute_script("arguments[0].click();", no_trade_in_input)
            log_message("✅ 成功点击 '无折抵换购'")
        except Exception as e:
            log_message(f"❌ 无法点击 '无折抵换购'，错误：{str(e)}")
            driver.save_screenshot("error_click_noTradeIn.png")

        # 检查停止标志
        if check_stop_flag():
            return False

        # 尝试不同的定位方法来选择"不加 AppleCare+"
        try:
            # 健壮选择"不加 AppleCare+"，失败自动刷新页面重试，最多3次
            max_applecare_retries = 3
            for applecare_attempt in range(max_applecare_retries):
                try:
                    # 方法一：优先用data-autom属性（最稳妥）
                    no_applecare_radio = WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, "input[data-autom='noapplecare']"))
                    )
                    driver.execute_script("arguments[0].click();", no_applecare_radio)
                    log_message("✅ 成功选择 '不加 AppleCare+' (data-autom方式)")
                    break
                except Exception as e:
                    log_message("❌ data-autom方式失败，尝试name+value方式...")
                    try:
                        # 方法二：用name+value兜底
                        no_applecare_radio = WebDriverWait(driver, 10).until(
                            EC.presence_of_element_located((By.XPATH, "//input[@name='applecare-options' and (@value='' or @value='false')]"))
                        )
                        driver.execute_script("arguments[0].click();", no_applecare_radio)
                        log_message("✅ 成功选择 '不加 AppleCare+' (name+value方式)")
                        break
                    except Exception as e:
                        log_message("❌ name+value方式失败，尝试label文本方式...")
                        try:
                            # 方法三：用label文本兜底
                            label = WebDriverWait(driver, 10).until(
                                EC.presence_of_element_located((By.XPATH, "//label[contains(., '不加 AppleCare+')]"))
                            )
                            input_id = label.get_attribute("for")
                            no_applecare_radio = driver.find_element(By.ID, input_id)
                            driver.execute_script("arguments[0].click();", no_applecare_radio)
                            log_message("✅ 成功选择 '不加 AppleCare+' (label文本方式)")
                            break
                        except Exception as e:
                            log_message(f"❌ 第{applecare_attempt+1}次尝试所有方式都失败，刷新页面重试... 错误：{str(e)}")
                            driver.save_screenshot(f"error_noAppleCare_retry{applecare_attempt+1}.png")
                            if applecare_attempt < max_applecare_retries - 1:
                                driver.refresh()
                                time.sleep(1)
                            else:
                                log_message("❌ 多次尝试后仍无法选择'不加 AppleCare+'，流程终止")
                                driver.save_screenshot("error_noAppleCare_final.png")
                                return False

        except Exception as e:
            log_message(f"❌ 无法选择 '不加 AppleCare+'，错误：{str(e)}")
            driver.save_screenshot("error_noAppleCare.png")

        # 检查停止标志
        if check_stop_flag():
            return False

        # 添加随机延迟，减少并发冲突
        random_delay = random.uniform(1, 3)
        log_message(f"⏳ 随机延迟 {random_delay:.1f} 秒，减少并发冲突...")
        time.sleep(random_delay)

        # 点击"添加到购物车"按钮,最多尝试3次
        max_retries = 3
        retry_count = 0
        while retry_count < max_retries:
            # 检查停止标志
            if check_stop_flag():
                return False
                
            try:
                # 等待页面完全加载，缩短基础等待时间
                WebDriverWait(driver, 10).until(
                    lambda d: d.execute_script("return document.readyState") == "complete"
                )
                
                # 添加随机等待，防止反爬虫检测
                time.sleep(random.uniform(0.5, 0.8))
                
                # 等待动态内容加载
                wait_for_element_present(driver, (By.CSS_SELECTOR, "button[name='add-to-cart'], button[data-autom='add-to-cart-button']"), 10)
                
                # 使用更精确的选择器，并缩短等待时间
                add_to_cart_button = WebDriverWait(driver, 8).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "button[name='add-to-cart'], button[data-autom='add-to-cart-button']"))
                )
                
                # 模拟人类行为：随机滚动到元素位置
                driver.execute_script("arguments[0].scrollIntoView({block: 'center', behavior: 'smooth'});", add_to_cart_button)
                time.sleep(random.uniform(0.5, 1.5))  # 随机等待滚动完成
                
                # 确保元素可交互
                wait_for_element_clickable(driver, (By.CSS_SELECTOR, "button[name='add-to-cart'], button[data-autom='add-to-cart-button']"), 5)
                
                # 模拟人类行为：鼠标悬停和随机暂停
                ActionChains(driver).move_to_element(add_to_cart_button).pause(random.uniform(0.3, 0.8)).perform()
                
                # 添加随机延迟，模拟人类思考时间
                time.sleep(random.uniform(0.5, 0.8))
                
                # 尝试多种点击方式，模拟真实用户行为
                try:
                    # 方式1：使用ActionChains模拟真实点击
                    ActionChains(driver).move_to_element(add_to_cart_button).pause(random.uniform(0.2, 0.5)).click().perform()
                except:
                    try:
                        # 方式2：JavaScript点击
                        driver.execute_script("arguments[0].click();", add_to_cart_button)
                    except:
                        try:
                            # 方式3：直接点击
                            add_to_cart_button.click()
                        except:
                            # 方式4：模拟键盘回车
                            add_to_cart_button.send_keys(Keys.RETURN)

                # 点击后随机等待，模拟人类反应时间
                time.sleep(random.uniform(0.5, 0.8))
                
                # 智能验证点击成功 - 多重检测机制
                click_verified = False
                verification_attempts = 3

                for verify_attempt in range(verification_attempts):
                    try:
                        # 方法1：检测"查看购物袋"按钮
                        WebDriverWait(driver, 5).until(
                            EC.presence_of_element_located((By.XPATH, "//button[contains(text(), '查看购物袋') or contains(text(), '查看购物车')]"))
                        )
                        click_verified = True
                        log_message("✅ 成功点击 '添加到购物车' 按钮（检测到查看购物袋按钮）")
                        break
                    except:
                        # 方法2：检测URL变化或页面元素变化
                        try:
                            # 检查是否有购物车相关的元素出现
                            cart_elements = driver.find_elements(By.XPATH, "//div[contains(@class, 'bag') or contains(@class, 'cart')]")
                            if len(cart_elements) > 0:
                                click_verified = True
                                log_message("✅ 成功点击 '添加到购物车' 按钮（检测到购物车元素）")
                                break
                        except:
                            pass

                        # 方法3：检测页面标题或URL变化
                        try:
                            if "bag" in driver.current_url.lower() or "购物" in driver.title:
                                click_verified = True
                                log_message("✅ 成功点击 '添加到购物车' 按钮（检测到页面变化）")
                                break
                        except:
                            pass

                    if verify_attempt < verification_attempts - 1:
                        log_message(f"⏳ 第{verify_attempt + 1}次验证失败，等待页面响应...")
                        time.sleep(2)

                if click_verified:
                    break
                else:
                    raise Exception("多次验证后仍未检测到添加购物车成功的标志")

            except Exception as e:
                retry_count += 1
                log_message(f"❌ 第 {retry_count} 次尝试点击'添加到购物车'按钮失败，错误：{str(e)}")
                # 保存错误发生时的页面HTML
                error_html_filename = f"error_addToCart_retry_{retry_count}.html"
                with open(error_html_filename, "w", encoding="utf-8") as f:
                    f.write(driver.page_source)
                log_message(f"📸 已保存错误页面HTML: {error_html_filename}")
                driver.save_screenshot(f"error_addToCart_retry_{retry_count}.png")

                if retry_count < max_retries:
                    log_message("🔄 智能重试策略...")
                    # 智能重试：不总是刷新页面，先尝试等待
                    if retry_count == 1:
                        log_message("⏳ 第1次重试：等待页面稳定...")
                        time.sleep(random.uniform(3, 5))
                    else:
                        log_message("🔄 第2次重试：刷新页面...")
                        driver.refresh()
                        time.sleep(random.uniform(3, 5))
                        # 重新等待页面加载
                        wait_for_page_load(driver, 10)
                else:
                    raise Exception("无法点击'添加到购物车'按钮，已达到最大重试次数")

        if retry_count >= max_retries:
            raise Exception("无法点击'添加到购物车'按钮，已达到最大重试次数")

        # 检查停止标志
        if check_stop_flag():
            return False

        # 等待"查看购物袋"按钮出现并点击它
        try:
            view_bag_button = WebDriverWait(driver, 20).until(
                EC.element_to_be_clickable((By.XPATH, "//button[text()='查看购物袋']"))
            )
            driver.execute_script("arguments[0].click();", view_bag_button)
            log_message("✅ 成功点击 '查看购物袋' 按钮")
        except Exception as e:
            log_message(f"❌ 无法点击'查看购物袋'按钮，错误：{str(e)}")
            driver.save_screenshot("error_viewBag.png")

        # 检查停止标志
        if check_stop_flag():
            return False

        # 确认已导航至购物车页面
        try:
            WebDriverWait(driver, 10).until(
                EC.url_contains("https://www.apple.com.cn/shop/bag")
            )
            log_message("✅ 已成功导航至购物车页面")
        except Exception as e:
            log_message(f"❌ 未能确认导航至购物车页面，当前URL: {driver.current_url}")
            driver.save_screenshot("error_confirmBag.png")

        # 检查停止标志
        if check_stop_flag():
            return False

        # 修改商品数量为 2
        try:
            # 找到下拉菜单的触发器并点击
            quantity_dropdown_trigger = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, "//div[@class='rs-quantity-selector']//span[@class='rs-quantity-icon form-dropdown-chevron']"))
            )
            driver.execute_script("arguments[0].click();", quantity_dropdown_trigger)

            # 再次查找并选择数量
            quantity_selector = WebDriverWait(driver, 30).until(
                EC.presence_of_element_located((By.XPATH, "//select[@class='rs-quantity-dropdown form-dropdown-select']"))
            )
            select_quantity = Select(quantity_selector)
            select_quantity.select_by_value('2')
            log_message("✅ 成功选择商品数量为2")
        except Exception as e:
            log_message(f"❌ 无法选择商品数量为2，错误：{str(e)}")
            driver.save_screenshot("error_selectQuantity.png")

        # 检查停止标志
        if check_stop_flag():
            return False

        # 点击"结账"按钮
        try:
            checkout_button = WebDriverWait(driver, 20).until(
                EC.element_to_be_clickable((By.ID, "shoppingCart.actions.navCheckout"))
            )
            driver.execute_script("arguments[0].click();", checkout_button)
            log_message("✅ 成功点击 '结账' 按钮")

            # 等待页面加载完成
            wait_for_page_load(driver, 10)

            # 点击结账后判断页面类型
            current_state = detect_page_state(driver)
            log_message(f"🔄 页面加载完成后检测到页面状态: {current_state}")
            if current_state == CheckoutPageState.LOGIN_SECURE_CHECKOUT:
                log_message("✅ 当前为【登陆-安全结账】页面，后续将处理登录、隐私弹窗等流程")
                # 处理隐私弹窗、登录、双重认证、信任浏览器
                handle_privacy_popup(driver, log_message)
                
                # 检查停止标志
                if check_stop_flag():
                    return False
                    
                two_fa_result = handle_two_factor_auth(driver, account, log_message)
                if not two_fa_result:
                    log_message("❌ 登录后双重认证处理失败")
                    return False
                    
                # 检查停止标志
                if check_stop_flag():
                    return False
                    
                handle_trust_browser(driver, log_message)
                log_message("✅ 成功处理登录、隐私弹窗、双重认证、信任浏览器流程")
            elif current_state == CheckoutPageState.ORDER_OPTIONS:
                log_message("✅ 当前为【订单选项-安全结账】页面，后续将直接进入填写送货地址等流程")
                result = fill_shipping_and_payment_flow(driver, account, log_message, test_mode=test_mode, stop_flag=stop_flag)
                if not result:
                    return False
            elif current_state == CheckoutPageState.LOGIN:
                log_message("⚠️ 检测到普通登录页面，尝试自动填充账号密码...")
                if not auto_login(driver, account, log_message):
                    log_message("❌ 自动登录失败，流程中断")
                    return False
                    
                # 检查停止标志
                if check_stop_flag():
                    return False
                    
                # 登录后处理可能存在的双重认证和信任此浏览器弹窗
                handle_two_factor_auth(driver, account, log_message)
                handle_trust_browser(driver, log_message)
                # 登录后重新检测页面状态并继续
                current_state = detect_page_state(driver)
                log_message(f"🔄 登录后页面状态: {current_state}")
                if current_state == CheckoutPageState.LOGIN or current_state == CheckoutPageState.UNKNOWN:
                    log_message("❌ 登录后仍未进入下单流程，流程中断")
                    driver.save_screenshot("error_login_still.png")
                    return False
                # 重新进入主流程分支
                if current_state == CheckoutPageState.ORDER_OPTIONS:
                    result = fill_shipping_and_payment_flow(driver, account, log_message, test_mode=test_mode, stop_flag=stop_flag)
                    if not result:
                        return False
                elif current_state == CheckoutPageState.PAYMENT_DETAILS:
                    result = fill_shipping_and_payment_flow(driver, account, log_message, test_mode=test_mode, stop_flag=stop_flag)
                    if not result:
                        return False
                else:
                    log_message(f"❌ 登录后进入未知页面状态: {current_state}，流程中断")
                    driver.save_screenshot("error_login_unknown.png")
                    return False
            else:
                log_message(f"❌ 检测到未知页面状态: {current_state}，自动化流程中断！")
                driver.save_screenshot("error_unknown_page.png")
                return False

        except Exception as e:
            log_message(f"❌ 无法填写账号密码，错误：{str(e)}")
            driver.save_screenshot("error_login.png")
            # 确保切回主frame
            try:
                driver.switch_to.default_content()
            except:
                pass

        log_message("🎉 自动化流程全部成功完成！")

        # 性能监控：记录成功
        account_end_time = time.time()
        total_time = account_end_time - account_start_time
        performance_monitor.record_attempt(account_email, success=True)
        performance_monitor.record_step_time("total_flow", total_time, account_email)
        log_message(f"📊 流程总耗时: {total_time:.2f}秒")

        return result

    except Exception as e:
        log_message(f"❌ 发生未预期的错误：{str(e)}")

        # 性能监控：记录失败
        account_end_time = time.time()
        total_time = account_end_time - account_start_time
        performance_monitor.record_attempt(account_email, success=False)
        performance_monitor.record_error(f"unexpected_error: {type(e).__name__}", account_email)
        performance_monitor.record_step_time("failed_flow", total_time, account_email)

        if driver:  # 检查driver是否存在
            driver.save_screenshot(f"unexpected_error_{int(time.time())}.png")
        return False

    finally:
        # 关闭浏览器
        if driver:
            async_quit_driver(driver)

        # 释放代理资源
        if use_proxy and account.get("email"):
            proxy_pool.release_proxy(account["email"])
            log_message(f"✅ 已释放账号 {account['email']} 的代理资源")

def run_parallel_automation(max_workers=3, target_urls=None, violent_mode=False):
    """并行运行多个账号的自动化流程"""
    global ui  # 使ui成为全局变量
    
    # 提前创建GUI，避免在关键时刻延迟
    print("🔄 初始化GUI界面...")
    ui = VerificationCodeUI()
    
    # 创建全局的SmartLinkDetector实例，避免重复创建
    global_detector = None
    target_url = None
    
    # 智能链接检测逻辑
    if target_urls is None:
        # 如果没有提供target_urls，尝试从配置文件加载
        print("🔍 尝试从配置文件加载监控链接...")
        global_detector = SmartLinkDetector()
        
        # 收集所有账号的抢购链接
        purchase_urls = set()
        for account in get_accounts():
            if account.get("url"):
                purchase_urls.add(account["url"])
        
        print(f"📋 发现 {len(purchase_urls)} 个抢购链接")
        
        # 检查配置文件中是否有监控链接
        if global_detector.config['monitored_links']:
            print(f"✅ 从配置文件加载到 {len(global_detector.config['monitored_links'])} 个监控链接")
            
            # 显示监控链接
            for i, link in enumerate(global_detector.config['monitored_links'], 1):
                print(f"  {i}. {link['name']} - {link['url']}")
            
            print("💡 说明：监控链接用于判断网站状态，抢购链接用于实际抢购")
            
            # 直接创建监控器，不重复添加到配置文件
            for link_config in global_detector.config['monitored_links']:
                monitor = LinkMonitor(
                    link_config['url'], 
                    link_config.get('check_interval', global_detector.config['default_check_interval']),
                    link_config.get('max_retries', global_detector.config['default_max_retries'])
                )
                global_detector.monitors[link_config['name']] = monitor
            
            # 启动所有监控器
            global_detector.start_all_monitors()
            
            # 等待任意一个监控链接可用
            print("⏰ 等待监控链接可用（判断网站状态）...")
            available_name, available_monitor = global_detector.wait_for_any_link(timeout=1800)  # 30分钟超时
            
            if available_name and available_monitor:
                print(f"🎉 监控链接可用！网站状态正常，可以开始抢购")
                print(f"📊 监控链接: {available_name} - {available_monitor.target_url}")
                # 这里不需要设置target_url，因为每个账号会使用自己的抢购链接
            else:
                print("❌ 所有监控链接都不可用，网站可能有问题")
                print("⚠️ 是否继续尝试抢购？")
                continue_anyway = input("继续尝试抢购? (y/n): ").strip().lower()
                if continue_anyway != 'y':
                    print("🛑 用户选择退出，程序结束")
                    return
        else:
            print("❌ 配置文件中没有监控链接")
            print("💡 建议：")
            print("   1. 选择模式4启动链接监控器添加监控链接")
            print("   2. 选择模式3进行链接配置管理")
            print("   3. 或直接开始抢购（不监控网站状态）")
            
            use_default = input("是否直接开始抢购? (y/n): ").strip().lower()
            if use_default == 'y':
                print("✅ 直接开始抢购流程")
            else:
                print("🛑 用户选择退出，程序结束")
                return
    else:
        # 如果提供了目标URL列表，先进行智能链接检测
        print("🔍 开始智能链接检测...")
        global_detector = SmartLinkDetector()
        
        # 添加所有目标链接到监控器
        for i, url in enumerate(target_urls):
            global_detector.add_link(url, f"target_link_{i}", check_interval=15, max_retries=30)
        
        # 启动所有监控器
        global_detector.start_all_monitors()
        
        # 等待任意一个链接可用
        print("⏰ 等待链接可用...")
        available_name, available_monitor = global_detector.wait_for_any_link(timeout=1800)  # 30分钟超时
        
        if available_name and available_monitor:
            print(f"🎉 检测到可用链接: {available_name}")
            target_url = available_monitor.target_url
        else:
            print("❌ 所有链接都不可用，使用默认链接")
            target_url = "https://www.apple.com.cn/shop/buy-iphone/iphone-16-pro/MYTN3CH/A"
    
    # 确保监控器已停止
    if global_detector:
        global_detector.stop_all_monitors()
    
    print(f"🚀 开始多账号并行自动化流程")
    print(f"📊 账号数量: {len(get_accounts())}")
    # 动态调整max_workers
    if violent_mode:
        max_workers = len(get_accounts())
        print(f"⚡ 暴力模式已启用，max_workers={max_workers}")
    
    # 简化账号链接显示
    print("📋 账号抢购链接配置:")
    for i, account in enumerate(get_accounts(), 1):
        purchase_url = account.get("url", "未配置")
        print(f"  {i}. {account['email']} → {purchase_url}")
    
    # 创建自动化任务
    def run_automation_tasks():
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 创建任务 - 每个账号使用自己的抢购链接
            future_to_account = {}
            print("🚀 启动自动化任务...")
            
            for account in get_accounts():
                # 使用账号配置中的抢购链接
                purchase_url = account.get("url")
                if not purchase_url:
                    # 如果没有配置抢购链接，使用默认链接
                    purchase_url = "https://www.apple.com.cn/shop/buy-iphone/iphone-16-pro/MYTN3CH/A"
                
                # 尝试读取全局代理设置
                use_proxy = False
                try:
                    if os.path.exists("config/proxy_enabled.json"):
                        with open("config/proxy_enabled.json", "r", encoding="utf-8") as f:
                            settings = json.load(f)
                            use_proxy = settings.get("enabled", False)
                except Exception:
                    pass
                
                future = executor.submit(
                    run_automation_for_account, 
                    account, 
                    use_proxy=use_proxy,  # 根据设置决定是否使用代理 
                    target_url=purchase_url
                )
                future_to_account[future] = account
            
            print(f"✅ 已启动 {len(get_accounts())} 个账号的自动化任务")
            
            # 等待所有任务完成
            for future in concurrent.futures.as_completed(future_to_account):
                account = future_to_account[future]
                try:
                    success = future.result()
                    if success:
                        print(f"✅ 账号 {account['email']} 操作成功完成")
                    else:
                        print(f"❌ 账号 {account['email']} 操作失败")
                except Exception as e:
                    print(f"❌ 账号 {account['email']} 发生异常：{str(e)}")
        
        # 所有任务完成后关闭GUI
        ui.root.quit()
    
    # 在新线程中运行自动化任务
    automation_thread = threading.Thread(target=run_automation_tasks)
    automation_thread.daemon = True
    automation_thread.start()
    
    # 在主线程中运行GUI
    ui.run()

# =====================
# 新增：UI界面控制账号启动与停止
# =====================
import threading
import tkinter as tk
from tkinter import ttk
import os
import platform

class AccountControllerUI:
    def __init__(self, accounts):
        global ui
        ui = self
        self.accounts = accounts
        self.root = tk.Tk()
        self.root.title("iPhone 自动化抢购神器")
        self.root.geometry("1500x1000")  # 增加窗口大小以容纳日志区域
        
        # 测试模式开关
        self.test_mode = True  # 默认开启测试模式
        
        # 加载保存的自动启动设置
        self.auto_start_enabled = self._load_auto_start_setting()
        
        # 监控链接开关
        self.link_monitoring_enabled = self._load_link_monitoring_setting()
        
        # 不自动显示代理状态，由用户手动点击测试按钮
        # self._show_proxy_status()
        
        # 监控器实例
        self.link_detector = None
        self.monitoring_active = False
        
        # 窗口居中显示
        self.center_window()
        
        self.account_threads = {}  # 账号邮箱 -> 线程对象
        self.stop_flags = {}       # 账号邮箱 -> threading.Event
        self.status_vars = {}      # 账号邮箱 -> tk.StringVar
        
        # 自动启动状态跟踪
        self.completed_accounts = set()  # 跟踪已完成的账号
        
        # 订单号缓存字典
        self.order_numbers = {}  # 账号邮箱 -> 订单号或"测试完成"
        
        # 日志显示相关
        self.log_text = None
        self.log_filter_var = None
        self.last_log_count = 0
        
        # 暴力模式开关
        self.violent_mode_enabled = self._load_violent_mode_setting()
        
        self._build_ui()
        self._refresh_status_loop()
        self._refresh_logs_loop()  # 启动日志刷新循环
        
        # 新增：验证码UI - 在主线程中创建，避免多线程问题
        self.verification_ui = VerificationCodeUI()
        # 将验证码UI的root设置为当前root的子窗口
        self.verification_ui.root.withdraw()  # 先隐藏验证码窗口

    def center_window(self):
        """将窗口居中显示"""
        # 更新窗口信息，确保获取正确的尺寸
        self.root.update_idletasks()
        
        # 获取屏幕尺寸
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        # 获取窗口尺寸
        window_width = self.root.winfo_width()
        window_height = self.root.winfo_height()
        
        # 计算居中位置
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        
        # 设置窗口位置
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")

    def _build_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 标题
        #title = ttk.Label(main_frame, text="iPhone自动化账号控制台", font=("Arial", 16, "bold"))
        #title.pack(pady=10)
        
        # 测试模式开关
        test_mode_frame = ttk.Frame(main_frame)
        test_mode_frame.pack(fill="x", pady=2)
        self.test_mode_var = tk.BooleanVar(value=self.test_mode)
        test_mode_check = ttk.Checkbutton(test_mode_frame, text="测试模式（不提交真实订单）", variable=self.test_mode_var, command=self._toggle_test_mode)
        test_mode_check.pack(side="left", padx=5)
        test_mode_tip = ttk.Label(test_mode_frame, text="关闭后将进行真实下单，请谨慎操作！", foreground="red")
        test_mode_tip.pack(side="left", padx=10)
        # 新增：将Apple ID账号管理按钮放在右上角
        ttk.Button(test_mode_frame, text="Apple ID账号管理", command=self.open_database_management).pack(side="right", padx=10)
        # 性能监控按钮
        ttk.Button(test_mode_frame, text="性能统计", command=self.show_performance_stats).pack(side="right", padx=5)
        
        # 自动启动选项
        auto_start_frame = ttk.Frame(main_frame)
        auto_start_frame.pack(fill="x", pady=2)
        self.auto_start_var = tk.BooleanVar(value=self.auto_start_enabled)
        auto_start_check = ttk.Checkbutton(auto_start_frame, text="自动启动所有账号（最多3个同时运行）", variable=self.auto_start_var, command=self._toggle_auto_start)
        auto_start_check.pack(side="left", padx=5)
        auto_start_tip = ttk.Label(auto_start_frame, text="勾选后程序启动时自动开始抢购", foreground="red")
        auto_start_tip.pack(side="left", padx=10)
        # （已移除暴力模式相关控件）
        # 监控链接开关
        link_monitor_frame = ttk.Frame(main_frame)
        link_monitor_frame.pack(fill="x", pady=2)
        self.link_monitor_var = tk.BooleanVar(value=self.link_monitoring_enabled)
        link_monitor_check = ttk.Checkbutton(link_monitor_frame, text="启用监控链接检测", variable=self.link_monitor_var, command=self._toggle_link_monitoring)
        link_monitor_check.pack(side="left", padx=5)
        # 恢复监控相关按钮
        self.start_monitor_btn = ttk.Button(link_monitor_frame, text="启动监控", command=self.start_link_monitoring)
        self.start_monitor_btn.pack(side="left", padx=10)
        self.stop_monitor_btn = ttk.Button(link_monitor_frame, text="停止监控", command=self.stop_link_monitoring, state="disabled")
        self.stop_monitor_btn.pack(side="left", padx=5)
        ttk.Button(link_monitor_frame, text="监控链接管理", command=self.open_monitor_links_management).pack(side="left", padx=5)
        # （已移除代理池相关控件）
        # 监控器实例
        self.link_detector = None
        # （已移除代理池相关控件）
        # 监控器实例
        self.monitoring_active = False
        # （已移除代理池相关控件）
        # 窗口居中显示
        self.center_window()
        
        # 账号列表框架
        account_frame = ttk.LabelFrame(main_frame, text="账号管理")
        account_frame.pack(fill="x", pady=5)
        
        # 新增：在表格上方添加操作按钮（全部启动和全部停止）
        op_btn_frame = ttk.Frame(account_frame)
        op_btn_frame.pack(fill="x", padx=10, pady=5)
        ttk.Button(op_btn_frame, text="全部启动", command=self.start_all).pack(side="left", padx=5)
        ttk.Button(op_btn_frame, text="全部停止", command=self.stop_all).pack(side="left", padx=5)
        ttk.Button(op_btn_frame, text="刷新账号", command=self.refresh_accounts).pack(side="right", padx=5)
        
        # 表头 - 添加订单号列和机型列
        columns = ("选择", "邮箱", "机型", "双重认证设备/手机号码", "密码", "分期方式", "收货信息", "状态", "订单号", "操作")
        self.tree = ttk.Treeview(account_frame, columns=columns, show="headings", height=8)
        # 设置列宽和标题
        column_widths = [60, 200, 250, 150, 120, 100, 220, 80, 120, 120]
        for i, col in enumerate(columns):
            self.tree.heading(col, text=col)
            self.tree.column(col, width=column_widths[i], anchor="center")
        self.tree.pack(fill="x", padx=10, pady=10)
        
        # 设置选择列的样式
        style = ttk.Style()
        style.configure("Treeview", rowheight=25)  # 增加行高以容纳复选框样式
        
        # 初始化每行
        for acc in self.accounts:
            email = acc["email"]
            remark = acc.get("remark", "")
            installment = acc.get("installment", "cmb")
            payment_method = get_installment_display_name(installment)
            status_var = tk.StringVar(value="🔴未启动")
            self.status_vars[email] = status_var
            model_display = SKU_DISPLAY_MAP.get(acc.get("url", ""), acc.get("url", ""))
            shipping_info = acc.get('shipping_info', {})
            shipping_str = f"{shipping_info.get('name','')}/{shipping_info.get('phone','')}/{shipping_info.get('address','')}" if shipping_info else ''
            # 获取订单号（如果有缓存的话）
            order_number = self.order_numbers.get(email, "")
            self.tree.insert("", "end", iid=email, values=(
                "⬜", email, model_display, remark, acc.get("password", ""), payment_method, shipping_str, status_var.get(), order_number, "启动"
            ))
        
        # 绑定点击事件
        self.tree.bind("<Button-1>", self.on_tree_click)
        
        # 运行状态显示
        status_frame = ttk.Frame(account_frame)
        status_frame.pack(pady=5)
        self.running_status_var = tk.StringVar(value="当前运行: 0/3 个账号")
        running_status_label = ttk.Label(status_frame, textvariable=self.running_status_var, font=("Arial", 15, "bold"), foreground="red")
        running_status_label.pack()
        
        # 全局操作
        ctrl_frame = ttk.Frame(account_frame)
        ctrl_frame.pack(pady=5)
        
        # 新增：批量操作框架
        batch_frame = ttk.Frame(account_frame)
        batch_frame.pack(pady=5)
        ttk.Button(batch_frame, text="全选", command=self.select_all_accounts).pack(side="left", padx=5)
        ttk.Button(batch_frame, text="取消全选", command=self.deselect_all_accounts).pack(side="left", padx=5)
        ttk.Button(batch_frame, text="批量修改机型", command=self.batch_modify_models).pack(side="left", padx=5)
        
        # 日志显示框架
        log_frame = ttk.LabelFrame(main_frame, text="实时日志")
        log_frame.pack(fill="both", expand=True, pady=5)
        
        # 日志控制栏
        log_control_frame = ttk.Frame(log_frame)
        log_control_frame.pack(fill="x", padx=10, pady=5)
        
        ttk.Label(log_control_frame, text="账号筛选:").pack(side="left")
        self.log_filter_var = tk.StringVar(value="全部账号")
        log_filter_combo = ttk.Combobox(log_control_frame, textvariable=self.log_filter_var, state="readonly", width=30)
        log_filter_combo.pack(side="left", padx=5)
        
        # 获取所有账号邮箱
        account_emails = ["全部账号"] + [acc['email'] for acc in self.accounts]
        log_filter_combo['values'] = account_emails
        
        ttk.Button(log_control_frame, text="清空日志", command=self._clear_logs).pack(side="left", padx=5)
        ttk.Button(log_control_frame, text="暂停/继续", command=self._toggle_log_pause).pack(side="left", padx=5)
        
        # 音效控制
        self.sound_enabled_var = tk.BooleanVar(value=sound_manager.enabled)
        sound_check = ttk.Checkbutton(log_control_frame, text="音效", variable=self.sound_enabled_var, command=self._toggle_sound)
        sound_check.pack(side="left", padx=5)
        
        # 音效测试按钮
        ttk.Button(log_control_frame, text="测试音效", command=self._test_sound).pack(side="left", padx=5)
        
        # 日志显示区域
        log_display_frame = ttk.Frame(log_frame)
        log_display_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # 创建日志文本框
        self.log_text = tk.Text(log_display_frame, wrap="word", height=15, font=("Consolas", 12))
        self.log_text.pack(side="left", fill="both", expand=True)
        
        # 日志滚动条
        log_scrollbar = ttk.Scrollbar(log_display_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        log_scrollbar.pack(side="right", fill="y")
        
        # 日志暂停标志
        self.log_paused = False
        
        # 初始化监控按钮状态
        self._update_monitor_btn_state()
        # 初始化暴力模式可用状态
        self._update_violent_mode_state()
        # 新增：暴力模式与代理池放在最下面
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.pack(fill="x", pady=10)
        self.proxy_enabled_var = tk.BooleanVar(value=self._load_proxy_setting())
        proxy_check = ttk.Checkbutton(bottom_frame, text="启用代理池", variable=self.proxy_enabled_var, command=self._toggle_proxy_enabled)
        proxy_check.pack(side="left", padx=10)
        # 新增：查看代理池按钮
        proxy_status_btn = ttk.Button(bottom_frame, text="查看代理池", command=self._show_proxy_status)
        proxy_status_btn.pack(side="left", padx=5)
        self.violent_mode_var = tk.BooleanVar(value=self.violent_mode_enabled)
        self.violent_mode_check = ttk.Checkbutton(bottom_frame, text="暴力模式（监控到有货时全部账号同时抢购）", variable=self.violent_mode_var, command=self._toggle_violent_mode)
        self.violent_mode_check.pack(side="left", padx=10)
        violent_mode_tip = ttk.Label(bottom_frame, text="勾选后监控到有货时所有账号同时启动，不限并发！", foreground="red")
        violent_mode_tip.pack(side="left", padx=10)
        # 初始化暴力模式可用状态
        self._update_violent_mode_state()

    def _refresh_logs_loop(self):
        """日志刷新循环"""
        if not self.log_paused:
            self._update_log_display()
        self.root.after(500, self._refresh_logs_loop)  # 每500ms刷新一次
    
    def _update_log_display(self):
        """更新日志显示"""
        try:
            with log_lock:
                current_log_count = len(log_queue)
                
                # 如果有新日志，更新显示
                if current_log_count > self.last_log_count:
                    # 获取筛选条件
                    filter_account = self.log_filter_var.get()
                    
                    # 清空显示区域
                    self.log_text.delete(1.0, tk.END)
                    
                    # 显示符合条件的日志，排除"系统"的"没有可用账号可启动"消息
                    for log_entry in log_queue:
                        # 过滤掉系统的"没有可用账号可启动"日志
                        if log_entry['account'] == "系统" and "没有可用账号可启动" in log_entry['message']:
                            continue
                        
                        if filter_account == "全部账号" or log_entry['account'] == filter_account:
                            log_line = f"[{log_entry['timestamp']}] [{log_entry['account']}] {log_entry['message']}\n"
                            self.log_text.insert(tk.END, log_line)
                    
                    # 滚动到底部
                    self.log_text.see(tk.END)
                    
                    self.last_log_count = current_log_count
        except Exception as e:
            print(f"更新日志显示时出错: {e}")
    
    def _clear_logs(self):
        """清空日志"""
        if messagebox.askyesno("确认", "确定要清空所有日志吗？"):
            with log_lock:
                log_queue.clear()
            self.last_log_count = 0
            self.log_text.delete(1.0, tk.END)
    
    def _toggle_log_pause(self):
        """暂停/继续日志显示"""
        self.log_paused = not self.log_paused
        button_text = "继续" if self.log_paused else "暂停"
        # 更新按钮文本（这里简化处理，实际可以保存按钮引用）
        print(f"日志显示已{button_text}")
    
    def _toggle_sound(self):
        """切换音效开关"""
        enabled = self.sound_enabled_var.get()
        sound_manager.toggle_sound(enabled)
        status = "启用" if enabled else "禁用"
        print(f"音效已{status}")
        add_log("系统", f"音效已{status}")
    
    def _test_sound(self):
        """测试音效功能"""
        if sound_manager.enabled:
            add_log("系统", "🔊 正在测试音效...")
            # 播放三种音效
            sound_manager.play_success_sound()
            self.root.after(1000, sound_manager.play_alert_sound)
            self.root.after(2000, sound_manager.play_notification_sound)
            add_log("系统", "✅ 音效测试完成，如果您没有听到声音，请检查系统音量设置")
        else:
            add_log("系统", "⚠️ 音效已禁用，请先启用音效")

    def open_database_management(self):
        """打开Apple ID账号管理界面"""
        try:
            # 创建Apple ID账号管理UI实例，传入父窗口
            db_ui = DatabaseManagementUI(parent_window=self.root)
            
            # 监听窗口关闭事件，更新账号列表
            def on_db_window_close():
                # 重新获取账号列表
                self.refresh_accounts()
                db_ui.root.destroy()
            
            db_ui.root.protocol("WM_DELETE_WINDOW", on_db_window_close)
            
            print("✅ Apple ID账号管理界面已打开")
            
        except Exception as e:
            print(f"❌ 打开Apple ID账号管理界面失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def show_performance_stats(self):
        """显示性能统计窗口"""
        try:
            stats_window = tk.Toplevel(self.root)
            stats_window.title("性能统计报告")
            stats_window.geometry("800x600")
            stats_window.resizable(True, True)

            # 创建滚动文本框
            text_frame = ttk.Frame(stats_window)
            text_frame.pack(fill="both", expand=True, padx=10, pady=10)

            text_widget = tk.Text(text_frame, wrap="word", font=("Consolas", 10))
            scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)

            text_widget.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

            # 获取性能统计数据
            summary = performance_monitor.get_summary()
            detailed = performance_monitor.get_detailed_report()

            # 生成报告内容
            report = self._generate_performance_report(summary, detailed)

            # 显示报告
            text_widget.insert("1.0", report)
            text_widget.config(state="disabled")

            # 按钮框架
            button_frame = ttk.Frame(stats_window)
            button_frame.pack(pady=5)

            # 刷新按钮
            refresh_btn = ttk.Button(button_frame, text="刷新数据",
                                    command=lambda: self._refresh_performance_stats(text_widget))
            refresh_btn.pack(side="left", padx=5)

            # 导出按钮
            export_btn = ttk.Button(button_frame, text="导出报告",
                                   command=lambda: self._export_performance_report(detailed))
            export_btn.pack(side="left", padx=5)

            # 清空统计按钮
            clear_btn = ttk.Button(button_frame, text="清空统计",
                                  command=lambda: self._clear_performance_stats(text_widget))
            clear_btn.pack(side="left", padx=5)

        except Exception as e:
            print(f"❌ 打开性能统计窗口失败: {str(e)}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("错误", f"打开性能统计窗口失败:\n{str(e)}")

    def _generate_performance_report(self, summary, detailed):
        """生成性能报告文本"""
        report = []
        report.append("=" * 60)
        report.append("🚀 iPhone抢购机器人 - 性能统计报告")
        report.append("=" * 60)
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")

        # 总体统计
        report.append("📊 总体统计")
        report.append("-" * 30)
        report.append(f"总尝试次数: {summary['total_attempts']}")
        report.append(f"成功次数: {summary['successful_attempts']}")
        report.append(f"失败次数: {summary['failed_attempts']}")
        report.append(f"成功率: {summary['success_rate']:.2f}%")
        if summary['total_time'] > 0:
            report.append(f"总耗时: {summary['total_time']:.2f}秒")
        report.append(f"账号数量: {summary['account_count']}")
        report.append("")

        # 步骤耗时统计
        if summary['avg_step_times']:
            report.append("⏱️ 平均步骤耗时")
            report.append("-" * 30)
            for step, avg_time in summary['avg_step_times'].items():
                report.append(f"{step}: {avg_time:.2f}秒")
            report.append("")

        # 错误统计
        if summary['top_errors']:
            report.append("❌ 主要错误类型")
            report.append("-" * 30)
            for error, count in summary['top_errors'].items():
                report.append(f"{error}: {count}次")
            report.append("")

        # 账号性能详情
        if detailed['account_performance']:
            report.append("👤 账号性能详情")
            report.append("-" * 30)
            for email, perf in detailed['account_performance'].items():
                success_rate = (perf['successes'] / perf['attempts'] * 100) if perf['attempts'] > 0 else 0
                report.append(f"📧 {email}")
                report.append(f"  尝试: {perf['attempts']} | 成功: {perf['successes']} | 失败: {perf['failures']}")
                report.append(f"  成功率: {success_rate:.2f}%")
                if 'step_times' in perf and perf['step_times']:
                    report.append("  步骤耗时:")
                    for step, times in perf['step_times'].items():
                        avg_time = sum(times) / len(times) if times else 0
                        report.append(f"    {step}: {avg_time:.2f}秒")
                report.append("")

        if not detailed['account_performance']:
            report.append("📝 暂无性能数据")
            report.append("请运行自动化流程后再查看统计信息")

        return "\n".join(report)

    def _refresh_performance_stats(self, text_widget):
        """刷新性能统计显示"""
        summary = performance_monitor.get_summary()
        detailed = performance_monitor.get_detailed_report()
        report = self._generate_performance_report(summary, detailed)

        text_widget.config(state="normal")
        text_widget.delete("1.0", "end")
        text_widget.insert("1.0", report)
        text_widget.config(state="disabled")

    def _export_performance_report(self, detailed_data):
        """导出性能报告到文件"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"performance_report_{timestamp}.json"

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(detailed_data, f, indent=2, ensure_ascii=False, default=str)

            messagebox.showinfo("导出成功", f"性能报告已导出到: {filename}")
        except Exception as e:
            messagebox.showerror("导出失败", f"导出性能报告时出错: {str(e)}")

    def _clear_performance_stats(self, text_widget):
        """清空性能统计数据"""
        result = messagebox.askyesno("确认清空", "确定要清空所有性能统计数据吗？此操作不可恢复。")
        if result:
            # 重新初始化性能监控器
            global performance_monitor
            performance_monitor = PerformanceMonitor()

            # 刷新显示
            self._refresh_performance_stats(text_widget)
            messagebox.showinfo("清空完成", "性能统计数据已清空")

    def refresh_accounts(self):
        """刷新账号列表"""
        try:
            self.accounts = get_accounts()
            for item in self.tree.get_children():
                self.tree.delete(item)
            for acc in self.accounts:
                email = acc["email"]
                remark = acc.get("remark", "")
                installment = acc.get("installment", "cmb")
                payment_method = get_installment_display_name(installment)
                if email not in self.status_vars:
                    status_var = tk.StringVar(value="🔴未启动")
                    self.status_vars[email] = status_var
                else:
                    status_var = self.status_vars[email]
                model_display = SKU_DISPLAY_MAP.get(acc.get("url", ""), acc.get("url", ""))
                shipping_info = acc.get('shipping_info', {})
                shipping_str = f"{shipping_info.get('name','')}/{shipping_info.get('phone','')}/{shipping_info.get('address','')}" if shipping_info else ''
                # 获取订单号（如果有缓存的话）
                order_number = self.order_numbers.get(email, "")
                self.tree.insert("", "end", iid=email, values=(
                    "⬜", email, model_display, remark, acc.get("password", ""), payment_method, shipping_str, status_var.get(), order_number, "启动"
                ))
            print(f"✅ 账号列表已刷新，共 {len(self.accounts)} 个账号")
        except Exception as e:
            print(f"❌ 刷新账号列表失败: {str(e)}")

    def _refresh_status_loop(self):
        # 计算当前运行的账号数量
        running_count = sum(1 for acc in self.accounts if 
                           acc["email"] in self.account_threads and 
                           self.account_threads[acc["email"]].is_alive())
        
        # 更新运行状态显示
        self.running_status_var.set(f"当前运行: {running_count}/3 个账号")
        
        for acc in self.accounts:
            email = acc["email"]
            status = self.status_vars[email].get()
            installment = acc.get("installment", "cmb")
            payment_method = get_installment_display_name(installment)
            
            # 更新状态和支付方式
            self.tree.set(email, column="状态", value=status)
            self.tree.set(email, column="分期方式", value=payment_method)
            
            # 根据状态更新操作列文本
            if status == "未启动" or status == "已停止":
                self.tree.set(email, column="操作", value="启动")
            elif status == "运行中":
                self.tree.set(email, column="操作", value="停止")
            elif status == "已完成":
                self.tree.set(email, column="操作", value="启动")
            elif status.startswith("异常") or status == "失败":
                self.tree.set(email, column="操作", value="启动")
        self.root.after(1000, self._refresh_status_loop)

    def on_tree_click(self, event):
        item = self.tree.identify_row(event.y)
        column = self.tree.identify_column(event.x)
        if not item:
            return
            
        # 添加调试信息
        print(f"点击事件: item={item}, column={column}")
        add_log("系统", f"点击事件: item={item}, column={column}")
        
        # 获取所有列的信息
        columns = self.tree["columns"]
        print(f"表格列: {columns}")
        add_log("系统", f"表格列: {columns}")
        
        # 获取点击位置的详细信息
        region = self.tree.identify_region(event.x, event.y)
        print(f"点击区域: {region}")
        add_log("系统", f"点击区域: {region}")
        
        # 获取点击的列名
        try:
            column_name = self.tree.heading(column)["text"]
            print(f"点击的列名: {column_name}")
            add_log("系统", f"点击的列名: {column_name}")
        except:
            print(f"无法获取列名，列索引: {column}")
            add_log("系统", f"无法获取列名，列索引: {column}")
            
        if column == '#1':  # 选择列
            # 切换勾选状态
            current_value = self.tree.set(item, "选择")
            new_value = "🟩" if current_value == "⬜" else "⬜"
            self.tree.set(item, column="选择", value=new_value)
        elif column == '#9' and item:  # 订单号列（第9列，索引从1开始）
            email = item
            order_number = self.tree.set(email, "订单号")
            print(f"订单号列点击: email={email}, order_number={order_number}")
            add_log("系统", f"订单号列点击: email={email}, order_number={order_number}")
            
            # 添加更多调试信息
            print(f"列索引: {column}, 列名: 订单号")
            add_log("系统", f"列索引: {column}, 列名: 订单号")
            
            # 检查订单号是否为空
            if not order_number:
                print("订单号为空")
                add_log("系统", "订单号为空")
                messagebox.showinfo("提示", "该账号暂无订单号")
                return
            
            if order_number and order_number != "" and order_number != "测试完成":
                # 确认是否打开订单页面
                result = messagebox.askyesno("打开订单页面", 
                    f"是否在Chrome中打开订单页面？\n\n"
                    f"账号: {email}\n"
                    f"订单号: {order_number}")
                if result:
                    print(f"用户确认打开订单页面: {order_number}")
                    add_log("系统", f"用户确认打开订单页面: {order_number}")
                    self.open_order_in_chrome(order_number, email)
            elif order_number == "测试完成":
                messagebox.showinfo("提示", "这是测试模式的订单，无法打开真实订单页面")
            else:
                messagebox.showinfo("提示", "该账号暂无订单号")
        elif column == '#10' and item:  # 操作列现在是第10列
            email = item
            op = self.tree.set(email, "操作")
            if op == "启动":
                self.start_account(email)
                self.tree.set(email, column="操作", value="停止")
            elif op == "停止":
                self.stop_account(email)
                self.tree.set(email, column="操作", value="启动")

    def start_account(self, email):
        if email in self.account_threads and self.account_threads[email].is_alive():
            return  # 已在运行
            
        # 添加完整的配置提醒（仅在手动启动时显示，自动启动时不显示）
        if not hasattr(self, '_auto_starting') or not self._auto_starting:
            config_info = self._get_current_config_info()
            result = messagebox.askyesno("启动账号确认", 
                f"🚀 启动账号确认\n\n"
                f"账号: {email}\n\n"
                f"当前配置：\n"
                f"✅ 测试模式：{'是' if config_info['test_mode'] else '否'}\n"
                f"✅ 启动代理：{'是' if config_info['proxy_enabled'] else '否'}\n\n"
                f"⚠️ 重要提醒：\n"
                f"• 测试模式：{'不会真正提交订单' if config_info['test_mode'] else '将真实提交订单'}\n"
                f"• 代理池：{'将使用代理进行抢购' if config_info['proxy_enabled'] else '将使用直连进行抢购'}\n\n"
                f"是否确认启动该账号？")
            if not result:
                add_log("系统", f"用户取消启动账号 {email}（配置确认）")
                return
            add_log("系统", f"用户确认启动账号 {email} - 配置：测试模式={config_info['test_mode']}, 代理={config_info['proxy_enabled']}")
                
        acc = next(a for a in self.accounts if a["email"] == email)
        stop_flag = threading.Event()
        self.stop_flags[email] = stop_flag
        self.status_vars[email].set("✅运行中")
        def log_callback(msg):
            self.status_vars[email].set("✅运行中")
            # 添加到全局日志队列
            add_log(email, msg)
        def run():
            try:
                # 根据代理设置决定是否启用代理
                use_proxy = self.proxy_enabled_var.get()
                result = run_automation_for_account(acc, log_callback=log_callback, use_proxy=use_proxy, test_mode=self.test_mode, stop_flag=stop_flag)
                if stop_flag.is_set():
                    self.status_vars[email].set("已停止")
                elif result:
                    # 保存订单号到缓存并更新表格
                    self.update_order_number(email, result)
                    self.status_vars[email].set("🎉已完成")
                    # 添加到已完成账号列表
                    self.completed_accounts.add(email)
                    # 播放成功音效
                    sound_manager.play_success_sound()
                    # 如果启用了自动启动，检查是否需要启动下一个账号
                    if self.auto_start_var.get():
                        self.root.after(random.randint(800, 1300), self._check_and_auto_start_next)
                else:
                    self.status_vars[email].set("❌失败")
                    # 添加到已完成账号列表（失败也算完成）
                    self.completed_accounts.add(email)
                    # 如果启用了自动启动，检查是否需要启动下一个账号
                    if self.auto_start_var.get():
                        self.root.after(random.randint(800, 1300), self._check_and_auto_start_next)
            except Exception as e:
                self.status_vars[email].set(f"⚠️异常: {e}")
                # 添加到已完成账号列表（异常也算完成）
                self.completed_accounts.add(email)
                # 如果启用了自动启动，检查是否需要启动下一个账号
                if self.auto_start_var.get():
                    self.root.after(random.randint(800, 1300), self._check_and_auto_start_next)
        t = threading.Thread(target=run, daemon=True)
        self.account_threads[email] = t
        t.start()

    def stop_account(self, email):
        if email in self.stop_flags:
            self.stop_flags[email].set()
            self.status_vars[email].set("⏹️已停止")

    def start_all(self):
        """启动所有账号，但限制最多同时运行3个"""
        # 获取当前正在运行的账号数量
        running_count = sum(1 for acc in self.accounts if 
                           acc["email"] in self.account_threads and 
                           self.account_threads[acc["email"]].is_alive())
        
        # 计算还可以启动的账号数量
        max_concurrent = 3
        available_slots = max_concurrent - running_count
        
        if available_slots <= 0:
            print(f"⚠️ 当前已有 {running_count} 个账号在运行，已达到最大限制 {max_concurrent}")
            add_log("系统", f"⚠️ 当前已有 {running_count} 个账号在运行，已达到最大限制 {max_concurrent}")
            return
        
        # 获取未启动的账号
        not_started_accounts = []
        for acc in self.accounts:
            email = acc["email"]
            if email not in self.account_threads or not self.account_threads[email].is_alive():
                not_started_accounts.append(acc)
        
        # 限制启动数量
        accounts_to_start = not_started_accounts[:available_slots]
        
        print(f"🔄 启动 {len(accounts_to_start)} 个账号（当前运行: {running_count}，最大限制: {max_concurrent}）")
        add_log("系统", f"🔄 启动 {len(accounts_to_start)} 个账号（当前运行: {running_count}，最大限制: {max_concurrent}）")
        
        for acc in accounts_to_start:
            self.start_account(acc["email"])
        
        if len(not_started_accounts) > available_slots:
            remaining = len(not_started_accounts) - available_slots
            print(f"⏳ 还有 {remaining} 个账号等待启动（需要等待其他账号完成）")
            add_log("系统", f"⏳ 还有 {remaining} 个账号等待启动（需要等待其他账号完成）")

    def stop_all(self):
        for acc in self.accounts:
            self.stop_account(acc["email"])

    def _toggle_auto_start(self):
        """切换自动启动状态并保存设置"""
        enabled = self.auto_start_var.get()
        self._save_auto_start_setting(enabled)
        
        if enabled:
            print("🔄 自动启动功能已启用，将在2秒后开始自动启动账号...")
            add_log("系统", "🔄 自动启动功能已启用，将在2秒后开始自动启动账号...")
            add_log("系统", "💡 下次启动程序时，如果监控链接有效，将自动开始抢购")
            # 延迟2秒后开始自动启动
            self.root.after(random.randint(800, 1300), self._check_and_auto_start_next)
        else:
            print("⏹️ 自动启动功能已禁用")
            add_log("系统", "⏹️ 自动启动功能已禁用")
            add_log("系统", "💡 下次启动程序时，需要手动点击启动按钮")
    
    def _check_and_auto_start_next(self, violent_mode=False):
        if not self.auto_start_var.get():
            return  # 如果自动启动已禁用，停止检查

        # 新增：如果启用了监控链接检测，且还未监控通过，则先自动监控
        if self.link_monitor_var.get() and not hasattr(self, '_monitor_checked'):
            self._monitor_checked = False  # 标记监控是否已通过

        if self.link_monitor_var.get() and not getattr(self, '_monitor_checked', False):
            # 只自动监控一次
            def monitor_callback():
                self._monitor_checked = True
                add_log("系统", "✅ 自动监控通过，开始自动抢购流程")
                self._check_and_auto_start_next(violent_mode)  # 监控通过后继续自动抢购
            def monitor_thread():
                add_log("系统", "⏳ 自动启动前，先自动检测监控链接...")
                self.link_detector = SmartLinkDetector()
                self.link_detector.start_all_monitors()
                import time
                max_wait_time = 300
                start_time = time.time()
                while time.time() - start_time < max_wait_time:
                    available = self.link_detector.get_available_links()
                    if available:
                        for link in available:
                            add_log("系统", f"🎉 自动监控检测到可用链接：{link['name']} {link['url']}")
                        # 播放通知音效
                        sound_manager.play_notification_sound()
                        self.link_detector.stop_all_monitors()
                        self.root.after(100, monitor_callback)
                        return
                    time.sleep(1)
                add_log("系统", "❌ 自动监控超时，未检测到可用链接，自动抢购流程终止")
                self._monitor_checked = False
            import threading
            threading.Thread(target=monitor_thread, daemon=True).start()
            return  # 等待监控完成后再继续

        # 获取当前正在运行的账号数量
        running_count = sum(1 for acc in self.accounts if 
                           acc["email"] in self.account_threads and 
                           self.account_threads[acc["email"]].is_alive())
        # 获取未启动且未完成的账号
        available_accounts = []
        for acc in self.accounts:
            email = acc["email"]
            if (email not in self.account_threads or not self.account_threads[email].is_alive()) and email not in self.completed_accounts:
                available_accounts.append(acc)
        # 暴力模式：全部账号同时启动
        if violent_mode:
            # 设置自动启动标记，避免显示提醒对话框
            self._auto_starting = True
            for acc in available_accounts:
                self.start_account(acc["email"])
            # 重置标记
            self._auto_starting = False
            add_log("系统", f"暴力模式已启动 {len(available_accounts)} 个账号并发抢购！")
            return
        # 普通模式：最多3个并发
        if available_accounts and running_count < 3:
            next_account = available_accounts[0]
            print(f"🔄 自动启动下一个账号: {next_account['email']}")
            add_log("系统", f"🔄 自动启动下一个账号: {next_account['email']}")
            # 设置自动启动标记，避免显示提醒对话框
            self._auto_starting = True
            self.start_account(next_account['email'])
            # 重置标记
            self._auto_starting = False
        else:
            add_log("系统", "⚠️ 没有可用账号可启动")
        # 如果所有账号都已完成，清空已完成列表重新开始
        if len(self.completed_accounts) == len(self.accounts):
            print("🔄 所有账号已完成，清空状态重新开始...")
            add_log("系统", "🔄 所有账号已完成，清空状态重新开始...")
            self.completed_accounts.clear()
        # 继续检查（如果还有账号在运行或还有可用账号）
        if running_count > 0 or available_accounts:
            self.root.after(5000, lambda: self._check_and_auto_start_next(violent_mode))

    def run(self):
        # 设置样式
        style = ttk.Style()
        style.configure('Highlight.TLabelframe', borderwidth=2, relief="solid")
        style.configure('Highlight.TLabelframe.Label', foreground='red')
        
        # 启动主循环
        self.root.mainloop()

    def _toggle_test_mode(self):
        self.test_mode = self.test_mode_var.get()

    def _load_auto_start_setting(self):
        """从配置文件加载自动启动设置"""
        try:
            config_file = "auto_start_config.json"
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    enabled = config.get('auto_start_enabled', True)  # 默认True
                    print(f"📋 加载自动启动设置: {'启用' if enabled else '禁用'}")
                    return enabled
        except Exception as e:
            print(f"⚠️ 加载自动启动设置失败: {e}")
        print("📋 使用默认设置: 自动启动功能启用")
        return True  # 默认启用
    
    def _save_auto_start_setting(self, enabled):
        """保存自动启动设置到配置文件"""
        try:
            config_file = "auto_start_config.json"
            config = {
                'auto_start_enabled': enabled,
                'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            print(f"✅ 自动启动设置已保存: {'启用' if enabled else '禁用'}")
        except Exception as e:
            print(f"保存自动启动设置失败: {e}")
    
    def _toggle_auto_start(self):
        """切换自动启动状态并保存设置"""
        enabled = self.auto_start_var.get()
        self._save_auto_start_setting(enabled)
        
        if enabled:
            print("🔄 自动启动功能已启用，将在2秒后开始自动启动账号...")
            add_log("系统", "🔄 自动启动功能已启用，将在2秒后开始自动启动账号...")
            add_log("系统", "💡 下次启动程序时，如果监控链接有效，将自动开始抢购")
            # 延迟2秒后开始自动启动
            self.root.after(random.randint(800, 1300), self._check_and_auto_start_next)
        else:
            print("⏹️ 自动启动功能已禁用")
            add_log("系统", "⏹️ 自动启动功能已禁用")
            add_log("系统", "💡 下次启动程序时，需要手动点击启动按钮")

    def select_all_accounts(self):
        """全选所有账号"""
        for item in self.tree.get_children():
            self.tree.set(item, column="选择", value="🟩")

    def deselect_all_accounts(self):
        """取消全选所有账号"""
        for item in self.tree.get_children():
            self.tree.set(item, column="选择", value="⬜")

    def batch_modify_models(self):
        """批量修改机型"""
        # 获取选中的账号
        selected_emails = []
        for item in self.tree.get_children():
            if self.tree.set(item, "选择") == "🟩":
                selected_emails.append(item)
        
        if not selected_emails:
            messagebox.showwarning("警告", "请先选择要修改的账号")
            return
        
        # 创建批量修改对话框
        dialog = BatchModifyModelDialog(self.root, selected_emails)
        if dialog.result:
            new_model, new_url = dialog.result
            # 更新数据库
            db = AccountDatabase()
            success_count = 0
            for email in selected_emails:
                if db.update_account(email, {"url": new_url}):
                    success_count += 1
                    # 更新界面显示
                    model_display = SKU_DISPLAY_MAP.get(new_url, new_url)
                    self.tree.set(email, column="机型", value=model_display)
            
            messagebox.showinfo("完成", f"成功修改 {success_count}/{len(selected_emails)} 个账号的机型")
            # 刷新账号列表
            self.refresh_accounts()

    def _toggle_link_monitoring(self):
        """切换监控链接状态并保存设置"""
        enabled = self.link_monitor_var.get()
        self.link_monitoring_enabled = enabled
        self._save_link_monitoring_setting(enabled)
        
        # 更新按钮状态
        self._update_monitor_btn_state()
        
        if enabled:
            print("🔍 监控链接检测已启用")
            add_log("系统", "🔍 监控链接检测已启用")
            add_log("系统", "💡 下次启动程序时，将先检测监控链接状态再开始抢购")
        else:
            print("⏹️ 监控链接检测已禁用")
            add_log("系统", "⏹️ 监控链接检测已禁用")
            add_log("系统", "💡 下次启动程序时，将直接开始抢购，不检测网站状态")
    
    def _load_link_monitoring_setting(self):
        """加载监控链接设置"""
        try:
            config_file = "ui_settings.json"
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    return config.get('link_monitoring_enabled', True)
            return True  # 默认启用监控链接
        except Exception as e:
            print(f"加载监控链接设置失败: {e}")
            return True
    
    def _save_link_monitoring_setting(self, enabled):
        """保存监控链接设置"""
        try:
            config_file = "ui_settings.json"
            config = {}
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            
            config.update({
                'link_monitoring_enabled': enabled,
                'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            print(f"✅ 监控链接设置已保存: {'启用' if enabled else '禁用'}")
        except Exception as e:
            print(f"保存监控链接设置失败: {e}")

    def start_link_monitoring(self):
        # 只有勾选启用监控链接检测时才允许手动启动监控
        if not self.link_monitor_var.get():
            add_log("系统", "未勾选启用监控链接检测，无法启动监控")
            return
        if self.monitoring_active:
            messagebox.showinfo("提示", "监控已在运行中！")
            add_log("系统", "用户点击启动监控，但监控已在运行中")
            return
            
        # 添加完整的配置提醒
        config_info = self._get_current_config_info()
        result = messagebox.askyesno("启动监控确认", 
            f"🚀 启动监控确认\n\n"
            f"当前配置：\n\n"
            f" 测试模式：{'❌' if config_info['test_mode'] else '⚠️'} "
            f"{'将不会正式提交订单' if config_info['test_mode'] else '将正式提交真实订单'}\n\n"

            f" 自动启动：{'✅' if config_info['auto_start'] else '❌'} "
            f"{'有货将自动启动账号' if config_info['auto_start'] else '需手动启动所有账号'}\n\n"

            f" 启用代理：{'✅' if config_info['proxy_enabled'] else '❌'} "
            f"{'将使用代理进行抢购' if config_info['proxy_enabled'] else '将使用直连进行抢购'}\n\n"

            f" 暴力模式：{'✅' if config_info['violent_mode'] else '❌'} "
            f"{'所有账号将同时启动' if config_info['violent_mode'] else '3个账号将同时启动'}\n\n"
            
            
            f"是否确认启动监控？")
        if not result:
            add_log("系统", "用户取消启动监控（配置确认）")
            return
        add_log("系统", f"用户确认启动监控 - 配置：测试模式={config_info['test_mode']}, 自动启动={config_info['auto_start']}, 代理={config_info['proxy_enabled']}, 暴力模式={config_info['violent_mode']}")
        from threading import Thread
        self.link_detector = SmartLinkDetector()
        add_log("系统", f"用户点击启动监控，发现 {len(self.link_detector.monitors)} 个监控器")
        if len(self.link_detector.monitors) == 0:
            add_log("系统", "警告：没有配置任何监控链接，请先在监控链接管理中添加链接")
            messagebox.showwarning("警告", "没有配置任何监控链接，请先在监控链接管理中添加链接")
            return
        self.monitoring_active = True
        self.start_monitor_btn.config(state="disabled")
        self.stop_monitor_btn.config(state="normal")
        add_log("系统", "用户点击启动监控，开始监控所有链接")
        def monitor_thread():
            try:
                self.link_detector.start_all_monitors()
                add_log("系统", "所有监控器已启动，实时监控中...")
                import time
                max_wait_time = 300  # 最大等待5分钟
                check_interval = 1.0
                start_time = time.time()
                last_status_time = 0
                while time.time() - start_time < max_wait_time and self.monitoring_active:
                    available = self.link_detector.get_available_links()
                    if available:
                        for link in available:
                            add_log("系统", f"🎉 监控结果：{link['name']} 可用，URL: {link['url']}")
                        # 播放通知音效
                        sound_manager.play_notification_sound()
                        # 监控到有效链接后，判断是否自动启动所有账号
                        if self.auto_start_var.get():
                            add_log("系统", "✅ 检测到有效链接，自动启动所有账号抢购流程...")
                            # 判断暴力模式
                            violent_mode = self.violent_mode_var.get() if hasattr(self, 'violent_mode_var') else False
                            self.root.after(100, lambda: self._auto_start_all_accounts_after_monitor(violent_mode))
                        else:
                            add_log("系统", "✅ 检测到有效链接，但未勾选自动启动所有账号，不自动抢购")
                        break
                    # ... existing code ...
                    monitoring_count = 0
                    completed_count = 0
                    error_count = 0
                    for name, monitor in self.link_detector.monitors.items():
                        if monitor.monitoring:
                            monitoring_count += 1
                        elif hasattr(monitor, 'available') and monitor.available:
                            completed_count += 1
                        else:
                            error_count += 1
                    current_time = time.time()
                    if current_time - last_status_time >= 10:
                        if monitoring_count > 0:
                            add_log("系统", f"⏳ 监控进行中... 已运行 {int(current_time - start_time)} 秒，{monitoring_count} 个监控器运行中")
                        elif completed_count > 0:
                            add_log("系统", f"✅ 监控完成，{completed_count} 个监控器已完成")
                        elif error_count > 0:
                            add_log("系统", f"❌ 监控异常，{error_count} 个监控器出现错误")
                        last_status_time = current_time
                    if monitoring_count == 0:
                        if completed_count > 0:
                            add_log("系统", "✅ 监控结束，所有监控器已完成")
                        else:
                            add_log("系统", "❌ 监控结束，没有可用链接")
                        break
                    time.sleep(check_interval)
                else:
                    if self.monitoring_active:
                        self.link_detector.stop_all_monitors()
                        add_log("系统", "⏰ 监控超时（5分钟），强制停止所有监控器")
            except Exception as e:
                add_log("系统", f"❌ 监控线程异常: {e}")
                messagebox.showerror("监控异常", f"监控线程异常: {e}")
            finally:
                self.monitoring_active = False
                self.start_monitor_btn.config(state="normal")
                self.stop_monitor_btn.config(state="disabled")
        Thread(target=monitor_thread, daemon=True).start()

    def _auto_start_all_accounts_after_monitor(self, violent_mode=False):
        # 再次校验暴力模式前置条件
        if violent_mode and not self.proxy_enabled_var.get():
            add_log("系统", "⚠️ 启用暴力模式前必须先启用代理池，已自动取消暴力模式！")
            self.violent_mode_var.set(False)
            self._save_violent_mode_setting(False)
            self._update_violent_mode_state()
            messagebox.showwarning("警告", "启用暴力模式前必须先启用代理池！")
            violent_mode = False
        self._monitor_checked = True
        self.completed_accounts.clear()
        self.refresh_accounts()  # 修复：监控通过后刷新账号列表
        self._check_and_auto_start_next(violent_mode)

    def _check_and_auto_start_next(self, violent_mode=False):
        if not self.auto_start_var.get():
            return  # 如果自动启动已禁用，停止检查
        # 只有未勾选启用监控链接检测时，才直接自动抢购
        if self.link_monitor_var.get():
            # 如果监控未通过，不自动抢购
            if not getattr(self, '_monitor_checked', False):
                return
        # 获取当前正在运行的账号数量
        running_count = sum(1 for acc in self.accounts if 
                           acc["email"] in self.account_threads and 
                           self.account_threads[acc["email"]].is_alive())
        # 获取未启动且未完成的账号
        available_accounts = []
        for acc in self.accounts:
            email = acc["email"]
            if (email not in self.account_threads or not self.account_threads[email].is_alive()) and email not in self.completed_accounts:
                available_accounts.append(acc)
        # 暴力模式：全部账号同时启动
        if violent_mode:
            # 设置自动启动标记，避免显示提醒对话框
            self._auto_starting = True
            for acc in available_accounts:
                self.start_account(acc["email"])
            # 重置标记
            self._auto_starting = False
            add_log("系统", f"暴力模式已启动 {len(available_accounts)} 个账号并发抢购！")
            return
        # 普通模式：最多3个并发
        if available_accounts and running_count < 3:
            next_account = available_accounts[0]
            print(f"🔄 自动启动下一个账号: {next_account['email']}")
            add_log("系统", f"🔄 自动启动下一个账号: {next_account['email']}")
            # 设置自动启动标记，避免显示提醒对话框
            self._auto_starting = True
            self.start_account(next_account['email'])
            # 重置标记
            self._auto_starting = False
        else:
            add_log("系统", "⚠️ 没有可用账号可启动")
        # 如果所有账号都已完成，清空已完成列表重新开始
        #if len(self.completed_accounts) == len(self.accounts):
        #    print("🔄 所有账号已完成，清空状态重新开始...")
        #    add_log("系统", "🔄 所有账号已完成，清空状态重新开始...")
        #    self.completed_accounts.clear()
        # 继续检查（如果还有账号在运行或还有可用账号）
        if running_count > 0 or available_accounts:
            self.root.after(random.randint(800, 1300), lambda: self._check_and_auto_start_next(violent_mode))

    def _update_monitor_btn_state(self):
        if self.link_monitor_var.get():
            self.start_monitor_btn.config(state="normal")
        else:
            self.start_monitor_btn.config(state="disabled")

    def stop_link_monitoring(self):
        if self.link_detector:
            self.link_detector.stop_all_monitors()
        self.monitoring_active = False
        self.start_monitor_btn.config(state="normal")
        self.stop_monitor_btn.config(state="disabled")
        add_log("系统", "用户点击停止监控，所有监控器已停止")
        messagebox.showinfo("提示", "已停止所有监控！")

    def open_monitor_links_management(self):
        """打开监控链接管理界面"""
        try:
            # 创建数据库实例
            db = AccountDatabase()
            # 创建监控链接管理对话框
            dialog = MonitorLinksDialog(self.root, db)
            dialog.dialog.grab_set()  # 设置为模态对话框
            print("✅ 监控链接管理界面已打开")
        except Exception as e:
            print(f"❌ 打开监控链接管理界面失败: {str(e)}")
            import traceback
            traceback.print_exc()
            
    def _save_link_monitoring_setting(self, enabled):
        """保存链接监控设置"""
        try:
            # 创建配置目录（如果不存在）
            os.makedirs("config", exist_ok=True)
            
            # 保存设置
            with open("config/link_monitoring.json", "w", encoding="utf-8") as f:
                json.dump({"enabled": enabled}, f)
            
            return True
        except Exception as e:
            logging.error(f"保存链接监控设置失败: {str(e)}")
            return False
    
    def test_all_proxies(self):
        """测试所有代理"""
        # 确认是否要测试
        if not messagebox.askyesno("确认", "测试所有代理可能需要一些时间，确定要继续吗？"):
            return
            
        # 创建进度对话框
        progress_dialog = tk.Toplevel(self.root)
        progress_dialog.title("代理测试进度")
        progress_dialog.geometry("500x300")
        progress_dialog.transient(self.root)
        progress_dialog.grab_set()
        
        # 居中显示
        progress_dialog.update_idletasks()
        x = (self.root.winfo_screenwidth() - progress_dialog.winfo_width()) // 2
        y = (self.root.winfo_screenheight() - progress_dialog.winfo_height()) // 2
        progress_dialog.geometry(f"+{x}+{y}")
        
        # 进度显示
        ttk.Label(progress_dialog, text="正在使用与实际运行完全一致的环境测试代理，请稍候...").pack(pady=10)
        progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(progress_dialog, variable=progress_var, maximum=100)
        progress_bar.pack(fill="x", padx=20, pady=10)
        
        # 结果显示
        result_frame = ttk.Frame(progress_dialog)
        result_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # 创建结果列表
        columns = ("代理", "状态", "响应时间")
        result_tree = ttk.Treeview(result_frame, columns=columns, show="headings", height=10)
        result_tree.heading("代理", text="代理")
        result_tree.heading("状态", text="状态")
        result_tree.heading("响应时间", text="响应时间(秒)")
        result_tree.column("代理", width=250)
        result_tree.column("状态", width=80)
        result_tree.column("响应时间", width=100)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(result_frame, orient="vertical", command=result_tree.yview)
        result_tree.configure(yscrollcommand=scrollbar.set)
        result_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 获取代理池实例
        global proxy_pool
        if not proxy_pool:
            proxy_pool = ProxyPool()
        
        # 底部按钮
        button_frame = ttk.Frame(progress_dialog)
        button_frame.pack(fill="x", padx=20, pady=10)
        close_button = ttk.Button(button_frame, text="关闭", command=progress_dialog.destroy)
        close_button.pack(side="right")
        
        # 状态标签
        status_var = tk.StringVar(value="准备测试...")
        status_label = ttk.Label(progress_dialog, textvariable=status_var)
        status_label.pack(pady=5)
        
        def run_test():
            # 清空结果列表
            for item in result_tree.get_children():
                result_tree.delete(item)
            
            # 获取代理列表
            proxies = proxy_pool.proxies
            if not proxies:
                status_var.set("没有找到代理配置，请先添加代理")
                return
                
            status_var.set(f"测试 {len(proxies)} 个代理...")
            working_count = 0
            
            # 测试每个代理
            for i, proxy in enumerate(proxies):
                # 更新进度
                progress = (i + 1) / len(proxies) * 100
                progress_var.set(progress)
                
                # 显示当前测试的代理
                proxy_str = f"{proxy['host']}:{proxy['port']}"
                if proxy.get('username'):
                    proxy_str += f" (带验证)"
                status_var.set(f"测试代理 ({i+1}/{len(proxies)}): {proxy_str}")
                progress_dialog.update()
                
                # 测试代理
                try:
                    host = proxy['host']
                    port = proxy['port']
                    username = proxy.get('username')
                    password = proxy.get('password')
                    
                    working, speed = test_proxy(host, port, username, password)
                    
                    if working:
                        status = "可用"
                        speed_str = f"{speed:.3f}"
                        working_count += 1
                        # 使用绿色标记可用代理
                        result_tree.insert("", "end", values=(proxy_str, status, speed_str), tags=("working",))
                    else:
                        status = "不可用"
                        speed_str = "-"
                        # 使用红色标记不可用代理
                        result_tree.insert("", "end", values=(proxy_str, status, speed_str), tags=("not_working",))
                except Exception as e:
                    status = "错误"
                    speed_str = "-"
                    result_tree.insert("", "end", values=(proxy_str, status, str(e)), tags=("error",))
            
            # 配置标签颜色
            result_tree.tag_configure("working", background="lightgreen")
            result_tree.tag_configure("not_working", background="lightpink")
            result_tree.tag_configure("error", background="lightyellow")
            
            # 更新代理池的可用代理列表，使用浏览器进行测试
            proxy_pool.test_all_proxies(use_browser=False)
            
            # 更新状态
            status_var.set(f"测试完成: {working_count}/{len(proxies)} 个代理可用")
            add_log("系统", f"代理测试完成: {working_count}/{len(proxies)} 个代理可用")
            
            # 弹出结果通知
            if working_count > 0:
                messagebox.showinfo("测试结果", f"测试完成: {working_count}/{len(proxies)} 个代理可用\n\n可用代理已加入代理池，自动化程序将优先使用速度最快的代理。")
            else:
                messagebox.showwarning("测试结果", f"测试完成: 所有代理均不可用\n\n请检查代理配置是否正确，或者尝试更换代理服务商。")
        
        # 在新线程中运行测试
        threading.Thread(target=run_test, daemon=True).start()

    def _toggle_proxy_enabled(self):
        enabled = self.proxy_enabled_var.get()
        self._save_proxy_setting(enabled)
        add_log("系统", f"代理池已{'启用' if enabled else '关闭'}")
        self._update_violent_mode_state()
        # 如果代理池关闭，自动取消暴力模式
        if not enabled:
            self.violent_mode_var.set(False)
            self._toggle_violent_mode()

    def _load_proxy_setting(self):
        """加载代理设置"""
        try:
            if os.path.exists("config/proxy_enabled.json"):
                with open("config/proxy_enabled.json", "r", encoding="utf-8") as f:
                    settings = json.load(f)
                    return settings.get("enabled", False)
            return False  # 默认禁用代理
        except Exception as e:
            logging.error(f"加载代理设置失败: {str(e)}")
            return False

    def _save_proxy_setting(self, enabled):
        """保存代理设置"""
        try:
            # 创建配置目录（如果不存在）
            os.makedirs("config", exist_ok=True)
            
            # 保存设置
            with open("config/proxy_enabled.json", "w", encoding="utf-8") as f:
                json.dump({"enabled": enabled}, f)
            
            return True
        except Exception as e:
            logging.error(f"保存代理设置失败: {str(e)}")
            return False
            
    def _show_proxy_status(self):
        """弹窗显示代理池状态"""
        global proxy_pool
        if not proxy_pool:
            proxy_pool = ProxyPool()
        # 创建弹窗
        win = tk.Toplevel(self.root)
        win.title("代理池状态")
        win.geometry("520x400")
        # 表格
        columns = ("IP", "端口", "可用", "响应时间")
        tree = ttk.Treeview(win, columns=columns, show="headings")
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=110, anchor="center")
        tree.pack(fill="both", expand=True, padx=10, pady=10)
        # 刷新表格内容的函数
        def refresh_table():
            for item in tree.get_children():
                tree.delete(item)
            if hasattr(proxy_pool, 'proxies') and proxy_pool.proxies:
                for proxy in proxy_pool.proxies:
                    print(f"调试：{proxy['host']}:{proxy['port']} working={proxy.get('working')}")  # 调试输出
                    ip = proxy.get("host", "")
                    port = proxy.get("port", "")
                    working = "可用" if proxy.get("working", False) else "不可用"
                    speed = f"{proxy.get('speed', '-')}"
                    tree.insert("", "end", values=(ip, port, working, speed))
            else:
                tree.insert("", "end", values=("暂无代理", "", "", ""))
        refresh_table()
        # 立即测试按钮
        def on_test():
            # 弹窗内测试代理，测试后刷新表格
            if hasattr(proxy_pool, 'test_all_proxies'):
                proxy_pool.test_all_proxies(use_browser=False)  # 改为requests方式
            refresh_table()
        btn_frame = ttk.Frame(win)
        btn_frame.pack(pady=10)
        ttk.Button(btn_frame, text="立即测试", command=on_test).pack(side="left", padx=10)
        ttk.Button(btn_frame, text="关闭", command=win.destroy).pack(side="left", padx=10)

    def _toggle_violent_mode(self):
        enabled = self.violent_mode_var.get()
        # 再次校验代理池是否启用
        if enabled and not self.proxy_enabled_var.get():
            self.violent_mode_var.set(False)
            self._save_violent_mode_setting(False)
            add_log("系统", "⚠️ 启用暴力模式前必须先启用代理池！")
            messagebox.showwarning("警告", "启用暴力模式前必须先启用代理池！")
            self._update_violent_mode_state()
            return
        self._save_violent_mode_setting(enabled)
        add_log("系统", f"暴力模式已{'启用' if enabled else '关闭'}")
        self._update_violent_mode_state()

    def _save_violent_mode_setting(self, enabled):
        try:
            os.makedirs("config", exist_ok=True)
            with open("config/violent_mode.json", "w", encoding="utf-8") as f:
                json.dump({"enabled": enabled}, f)
            return True
        except Exception as e:
            logging.error(f"保存暴力模式设置失败: {str(e)}")
            return False

    def _load_violent_mode_setting(self):
        try:
            if os.path.exists("config/violent_mode.json"):
                with open("config/violent_mode.json", "r", encoding="utf-8") as f:
                    settings = json.load(f)
                    return settings.get("enabled", False)
        except Exception as e:
            logging.error(f"加载暴力模式设置失败: {str(e)}")
        return False

    def _get_current_config_info(self):
        """获取当前配置信息"""
        config_info = {
            'test_mode': hasattr(self, 'test_mode_var') and self.test_mode_var.get(),
            'auto_start': hasattr(self, 'auto_start_var') and self.auto_start_var.get(),
            'proxy_enabled': hasattr(self, 'proxy_enabled_var') and self.proxy_enabled_var.get(),
            'violent_mode': hasattr(self, 'violent_mode_var') and self.violent_mode_var.get()
        }
        return config_info

    def _update_violent_mode_state(self):
        # 只有启用代理池时，暴力模式才可用，否则置灰且取消勾选
        pass
    
    def update_order_number(self, email, order_number):
        """更新指定账号的订单号"""
        self.order_numbers[email] = order_number
        self.tree.set(email, column="订单号", value=order_number)
    
    def open_order_in_chrome(self, order_number, email):
        """在Chrome中打开订单页面"""
        import subprocess
        import urllib.parse
        
        # 构建订单URL
        encoded_email = urllib.parse.quote(email)
        order_url = f"https://www.apple.com.cn/xc/cn/vieworder/{order_number}/{encoded_email}"
        
        print(f"准备打开订单页面: {order_url}")
        add_log("系统", f"准备打开订单页面: {order_url}")
        
        try:
            # 在macOS上打开Chrome
            print("尝试使用Chrome打开...")
            add_log("系统", "尝试使用Chrome打开...")
            subprocess.run(['open', '-a', 'Google Chrome', order_url], check=True)
            print("✅ Chrome打开成功")
            add_log("系统", f"✅ 已在Chrome中打开订单页面: {order_url}")
        except subprocess.CalledProcessError as e:
            print(f"Chrome打开失败: {e}")
            add_log("系统", f"Chrome打开失败: {e}")
            # 如果Chrome打开失败，尝试使用默认浏览器
            try:
                print("尝试使用默认浏览器打开...")
                add_log("系统", "尝试使用默认浏览器打开...")
                subprocess.run(['open', order_url], check=True)
                print("✅ 默认浏览器打开成功")
                add_log("系统", f"✅ 已在默认浏览器中打开订单页面: {order_url}")
            except Exception as e2:
                print(f"默认浏览器也失败: {e2}")
                add_log("系统", f"❌ 打开订单页面失败: {str(e2)}")
                messagebox.showerror("错误", f"无法打开订单页面:\n{order_url}\n\n错误: {str(e2)}")
        if hasattr(self, 'violent_mode_check'):
            if self.proxy_enabled_var.get():
                self.violent_mode_check.config(state="normal")
            else:
                self.violent_mode_check.config(state="disabled")
                self.violent_mode_var.set(False)
                self._save_violent_mode_setting(False)

def main_ui():
    print("\n🚀 启动账号控制台UI...\n")
    add_log("系统", "🚀 启动账号控制台UI...")
    ui = AccountControllerUI(get_accounts())
    ui.run()

def main():
    """主函数 - 直接启动账号控制台"""
    print("=" * 60)
    print("🎯 iPhone抢购机器人 - 数据库版本")
    print("=" * 60)
    print("🚀 正在启动账号控制台...")
    print("=" * 60)
    
    try:
        # 直接启动账号控制台
        main_ui()
    except KeyboardInterrupt:
        print("\n\n👋 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()













