#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能统计功能测试脚本
"""

import sys
import os
import time
import random

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入主程序中的性能监控类
try:
    from iPhonebot import PerformanceMonitor
    print("✅ 成功导入PerformanceMonitor类")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

def test_performance_monitor():
    """测试性能监控功能"""
    print("🚀 开始测试性能监控功能...")
    
    # 创建性能监控实例
    monitor = PerformanceMonitor()
    
    # 开始会话
    monitor.start_session()
    print("📊 已开始监控会话")
    
    # 模拟一些账号操作
    test_accounts = [
        "<EMAIL>",
        "<EMAIL>", 
        "<EMAIL>"
    ]
    
    for i, account in enumerate(test_accounts):
        print(f"🔄 模拟账号 {account} 的操作...")
        
        # 模拟各个步骤的耗时
        steps = ["login", "add_to_cart", "checkout", "payment"]
        success = random.choice([True, False, True, True])  # 75%成功率
        
        for step in steps:
            step_time = random.uniform(1, 5)  # 1-5秒随机耗时
            monitor.record_step_time(step, step_time, account)
            time.sleep(0.1)  # 短暂延迟模拟真实操作
        
        # 记录尝试结果
        monitor.record_attempt(account, success)
        
        if not success:
            # 模拟错误
            error_types = ["network_error", "timeout_error", "element_not_found"]
            error = random.choice(error_types)
            monitor.record_error(error, account)
            print(f"  ❌ 模拟错误: {error}")
        else:
            print(f"  ✅ 模拟成功")
    
    # 结束会话
    monitor.end_session()
    print("📊 已结束监控会话")
    
    # 获取统计摘要
    print("\n" + "="*50)
    print("📈 统计摘要:")
    print("="*50)
    
    summary = monitor.get_summary()
    for key, value in summary.items():
        if isinstance(value, float):
            print(f"{key}: {value:.2f}")
        elif isinstance(value, dict):
            print(f"{key}:")
            for sub_key, sub_value in value.items():
                if isinstance(sub_value, float):
                    print(f"  {sub_key}: {sub_value:.2f}")
                else:
                    print(f"  {sub_key}: {sub_value}")
        else:
            print(f"{key}: {value}")
    
    # 获取详细报告
    print("\n" + "="*50)
    print("📋 详细报告:")
    print("="*50)
    
    detailed = monitor.get_detailed_report()
    print(f"账号性能数据: {len(detailed['account_performance'])} 个账号")
    print(f"步骤耗时数据: {len(detailed['step_times'])} 个步骤")
    print(f"错误统计数据: {len(detailed['error_counts'])} 种错误")
    
    return monitor

def test_gui_integration():
    """测试GUI集成"""
    print("\n🖥️ 测试GUI集成...")
    
    try:
        import tkinter as tk
        from tkinter import ttk
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("性能统计测试")
        root.geometry("400x300")
        
        # 创建测试按钮
        def show_test_stats():
            try:
                # 这里应该调用实际的性能统计显示函数
                print("✅ 性能统计按钮点击测试成功")
                test_window = tk.Toplevel(root)
                test_window.title("测试统计窗口")
                test_window.geometry("600x400")
                
                label = tk.Label(test_window, text="这是一个测试统计窗口\n性能统计功能正常工作！", 
                               font=("Arial", 14), pady=50)
                label.pack()
                
                close_btn = ttk.Button(test_window, text="关闭", command=test_window.destroy)
                close_btn.pack(pady=20)
                
            except Exception as e:
                print(f"❌ 性能统计按钮测试失败: {e}")
        
        # 添加测试按钮
        test_btn = ttk.Button(root, text="测试性能统计", command=show_test_stats)
        test_btn.pack(pady=50)
        
        info_label = tk.Label(root, text="点击按钮测试性能统计功能\n如果能正常打开窗口，说明功能正常", 
                             font=("Arial", 12))
        info_label.pack(pady=20)
        
        close_btn = ttk.Button(root, text="关闭测试", command=root.destroy)
        close_btn.pack(pady=20)
        
        print("✅ GUI测试窗口已创建，请点击按钮测试")
        root.mainloop()
        
    except Exception as e:
        print(f"❌ GUI集成测试失败: {e}")

if __name__ == "__main__":
    print("🧪 性能统计功能测试开始")
    print("="*60)
    
    # 测试性能监控核心功能
    monitor = test_performance_monitor()
    
    # 询问是否进行GUI测试
    try:
        user_input = input("\n是否进行GUI集成测试？(y/n): ").lower().strip()
        if user_input in ['y', 'yes', '是']:
            test_gui_integration()
        else:
            print("跳过GUI测试")
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    
    print("\n🎉 测试完成！")
